
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.29% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>806/1635</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.87% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>205/411</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.71% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>144/315</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.12% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>761/1549</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="62.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 62%"></div><div class="cover-empty" style="width: 38%"></div></div>
	</td>
	<td data-value="62.66" class="pct medium">62.66%</td>
	<td data-value="916" class="abs medium">574/916</td>
	<td data-value="58.11" class="pct medium">58.11%</td>
	<td data-value="265" class="abs medium">154/265</td>
	<td data-value="66" class="pct medium">66%</td>
	<td data-value="150" class="abs medium">99/150</td>
	<td data-value="62.73" class="pct medium">62.73%</td>
	<td data-value="848" class="abs medium">532/848</td>
	</tr>

<tr>
	<td class="file low" data-value="src/__tests__"><a href="src/__tests__/index.html">src/__tests__</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="399" class="abs low">0/399</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="105" class="abs low">0/105</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="386" class="abs low">0/386</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/__tests__/data"><a href="src/__tests__/data/index.html">src/__tests__/data</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/api"><a href="src/api/index.html">src/api</a></td>
	<td data-value="63.13" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 63%"></div><div class="cover-empty" style="width: 37%"></div></div>
	</td>
	<td data-value="63.13" class="pct medium">63.13%</td>
	<td data-value="217" class="abs medium">137/217</td>
	<td data-value="34.74" class="pct low">34.74%</td>
	<td data-value="118" class="abs low">41/118</td>
	<td data-value="58.82" class="pct medium">58.82%</td>
	<td data-value="34" class="abs medium">20/34</td>
	<td data-value="63.55" class="pct medium">63.55%</td>
	<td data-value="214" class="abs medium">136/214</td>
	</tr>

<tr>
	<td class="file high" data-value="src/data"><a href="src/data/index.html">src/data</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="93.61" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.61" class="pct high">93.61%</td>
	<td data-value="94" class="abs high">88/94</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="16" class="abs medium">10/16</td>
	<td data-value="96.15" class="pct high">96.15%</td>
	<td data-value="26" class="abs high">25/26</td>
	<td data-value="93.47" class="pct high">93.47%</td>
	<td data-value="92" class="abs high">86/92</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T16:12:48.685Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    