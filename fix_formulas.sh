#!/bin/bash

# Fix remaining monthsToTarget calls in formula-validation-llm.test.ts

cd /Users/<USER>/WebstormProjects/er

# Fix the remaining ones manually
sed -i '' 's/formulas\.monthsToTarget(startingValue, 1500000, 1000, monthlyRate)/formulas.monthsToTarget(startingValue, 1500000, monthlyRate, 1000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(startingValue, 3000000, 2000, monthlyRate)/formulas.monthsToTarget(startingValue, 3000000, monthlyRate, 2000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(startingValue, 5000000, 5000, monthlyRate)/formulas.monthsToTarget(startingValue, 5000000, monthlyRate, 5000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(1000000, 3000000, 3000, monthlyRate)/formulas.monthsToTarget(1000000, 3000000, monthlyRate, 3000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(3000000, 2000000, 1000, 0.00565)/formulas.monthsToTarget(3000000, 2000000, 0.00565, 1000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(1000000, 2000000, 0, 0.00565)/formulas.monthsToTarget(1000000, 2000000, 0.00565, 0)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(1000000, 2000000, negativeContribution, 0.00565)/formulas.monthsToTarget(1000000, 2000000, 0.00565, negativeContribution)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(1000000, 3000000, 3000, 0.00565)/formulas.monthsToTarget(1000000, 3000000, 0.00565, 3000)/g' src/__tests__/formula-validation-llm.test.ts
sed -i '' 's/formulas\.monthsToTarget(portfolio, target, contribution, 0.00565)/formulas.monthsToTarget(portfolio, target, 0.00565, contribution)/g' src/__tests__/formula-validation-llm.test.ts