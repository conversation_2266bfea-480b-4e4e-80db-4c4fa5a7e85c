import { WealthTracker } from '../wealth-tracker';
import * as formulas from '../formulas';

describe('Formula Validation with LLM Expected Results', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
  });

  describe('Portfolio Starting Values to Target Tests', () => {
    // Using compound interest formula: FV = PV * (1 + r)^n + PMT * (((1 + r)^n - 1) / r)
    // Assuming 7% annual return (0.565% monthly) based on historical market averages
    const monthlyRate = 0.00565; // Conservative estimate

    describe('Starting with $1M portfolio', () => {
      const startingValue = 1000000;

      it('should calculate months to reach $3M with $3,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 3,000,000, PV = 1,000,000, PMT = 3,000, r = 0.00565
        // Using ln[(T·r + C)/(PV·r + C)] / ln(1+r)
        // Result: 149 months (12.4 years)
        
        const expectedMonthsLLM = 149;
        const months = formulas.monthsToTarget(startingValue, 3000000, monthlyRate, 3000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1); // Within 10 months tolerance
      });

      it('should calculate months to reach $2M with $2,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 2,000,000, PV = 1,000,000, PMT = 2,000, r = 0.00565
        // Result: 99 months (8.3 years)
        
        const expectedMonthsLLM = 99;
        const months = formulas.monthsToTarget(startingValue, 2000000, monthlyRate, 2000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });

      it('should calculate months to reach $1.5M with $5,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 1,500,000, PV = 1,000,000, PMT = 5,000, r = 0.00565
        // Result: 42 months (3.5 years)
        
        const expectedMonthsLLM = 42;
        const months = formulas.monthsToTarget(startingValue, 1500000, monthlyRate, 5000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });
    });

    describe('Starting with $1.2M portfolio', () => {
      const startingValue = 1200000;

      it('should calculate months to reach $3M with $3,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 3,000,000, PV = 1,200,000, PMT = 3,000, r = 0.00565
        // Result: 127 months (10.6 years)
        
        const expectedMonthsLLM = 127;
        const months = formulas.monthsToTarget(startingValue, 3000000, monthlyRate, 3000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });

      it('should calculate months to reach $2M with $4,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 2,000,000, PV = 1,200,000, PMT = 4,000, r = 0.00565
        // Result: 63 months (5.3 years)
        
        const expectedMonthsLLM = 63;
        const months = formulas.monthsToTarget(startingValue, 2000000, monthlyRate, 4000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });

      it('should calculate months to reach $1.5M with $1,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 1,500,000, PV = 1,200,000, PMT = 1,000, r = 0.00565
        // Result: 35 months (2.9 years)
        
        const expectedMonthsLLM = 35;
        const months = formulas.monthsToTarget(startingValue, 1500000, monthlyRate, 1000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });
    });

    describe('Starting with $2M portfolio', () => {
      const startingValue = 2000000;

      it('should calculate months to reach $3M with $2,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 3,000,000, PV = 2,000,000, PMT = 2,000, r = 0.00565
        // Result: 63 months (5.3 years)
        
        const expectedMonthsLLM = 63;
        const months = formulas.monthsToTarget(startingValue, 3000000, monthlyRate, 2000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });

      it('should calculate months to reach $5M with $5,000 monthly contribution', () => {
        // Formula Calculation:
        // FV = 5,000,000, PV = 2,000,000, PMT = 5,000, r = 0.00565
        // Result: 127 months (10.6 years)
        
        const expectedMonthsLLM = 127;
        const months = formulas.monthsToTarget(startingValue, 5000000, monthlyRate, 5000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });
    });
  });

  describe('Future Value Calculations', () => {
    const monthlyRate = 0.00565;

    it('should calculate future value after 5 years with $500K start and $3K monthly', () => {
      // LLM Calculation:
      // PV = 500,000, PMT = 3,000, r = 0.00565, n = 60 months
      // FV = 500,000 * (1.00565)^60 + 3,000 * (((1.00565)^60 - 1) / 0.00565)
      // FV ≈ 700,845 + 212,340 = 913,185
      
      const expectedFVLLM = 914663;
      const fv = formulas.futureValueWithContributions(500000, monthlyRate, 3000, 60);
      
      expect(fv).toBeCloseTo(expectedFVLLM, -3); // Within $1000 tolerance
    });

    it('should calculate future value after 10 years with $1M start and $5K monthly', () => {
      // LLM Calculation:
      // PV = 1,000,000, PMT = 5,000, r = 0.00565, n = 120 months
      // FV = 1,000,000 * (1.00565)^120 + 5,000 * (((1.00565)^120 - 1) / 0.00565)
      // FV ≈ 1,960,343 + 859,928 = 2,820,271
      
      const expectedFVLLM = 2821204;
      const fv = formulas.futureValueWithContributions(1000000, monthlyRate, 5000, 120);
      
      expect(fv).toBeCloseTo(expectedFVLLM, -3);
    });
  });

  describe('Required Contribution Calculations', () => {
    const monthlyRate = 0.00565;

    it('should calculate required monthly contribution from $800K to $2M in 10 years', () => {
      // LLM Calculation:
      // FV = 2,000,000, PV = 800,000, r = 0.00565, n = 120 months
      // PMT = (FV - PV * (1 + r)^n) / (((1 + r)^n - 1) / r)
      // PMT = (2,000,000 - 800,000 * 1.96) / 171.986
      // PMT ≈ (2,000,000 - 1,568,275) / 171.986 ≈ 2,510
      
      const expectedPMTLLM = 2510;
      const pmt = formulas.requiredContributionForTarget(800000, 2000000, monthlyRate, 120);
      
      expect(pmt).toBeCloseTo(expectedPMTLLM, -2); // Within $100 tolerance
    });

    it('should calculate required monthly contribution from $1.5M to $3M in 5 years', () => {
      // LLM Calculation:
      // FV = 3,000,000, PV = 1,500,000, r = 0.00565, n = 60 months
      // PMT ≈ 15,800
      
      const expectedPMTLLM = 12596;
      const pmt = formulas.requiredContributionForTarget(1500000, 3000000, monthlyRate, 60);
      
      expect(pmt).toBeCloseTo(expectedPMTLLM, -2);
    });
  });

  describe('Different Return Rate Scenarios', () => {
    describe('Conservative scenario (5% annual, 0.407% monthly)', () => {
      const monthlyRate = 0.00407;

      it('should calculate months from $1M to $3M with $3K monthly', () => {
        // Formula Calculation with conservative rate:
        // Slower growth means more months needed
        // n = 189 months (15.8 years)
        
        const expectedMonthsLLM = 189;
        const months = formulas.monthsToTarget(1000000, 3000000, monthlyRate, 3000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });
    });

    describe('Optimistic scenario (10% annual, 0.797% monthly)', () => {
      const monthlyRate = 0.00797;

      it('should calculate months from $1M to $3M with $3K monthly', () => {
        // Formula Calculation with optimistic rate:
        // Faster growth means fewer months needed
        // n = 114 months (9.5 years)
        
        const expectedMonthsLLM = 114;
        const months = formulas.monthsToTarget(1000000, 3000000, monthlyRate, 3000);
        
        expect(months).toBeCloseTo(expectedMonthsLLM, -1);
      });
    });
  });

  describe('Edge Cases and Validation', () => {
    it('should handle case where target is already reached', () => {
      const months = formulas.monthsToTarget(3000000, 2000000, 0.00565, 1000);
      expect(months).toBe(0);
    });

    it('should handle zero contribution scenario', () => {
      // With no contributions, relies purely on growth
      // From $1M to $2M at 0.565% monthly
      // n = ln(2) / ln(1.00565) ≈ 123 months
      
      const expectedMonthsLLM = 123;
      const months = formulas.monthsToTarget(1000000, 2000000, 0.00565, 0);
      
      expect(months).toBeCloseTo(expectedMonthsLLM, -1);
    });

    it('should handle negative growth scenario', () => {
      // If expenses exceed contributions
      const negativeContribution = -1000; // Net negative after expenses
      const months = formulas.monthsToTarget(1000000, 2000000, 0.00565, negativeContribution);
      
      // Should still eventually reach target through growth, but take longer
      expect(months).toBeGreaterThan(123); // Longer than zero contribution case
    });
  });

  describe('Real-world Validation with WealthTracker', () => {
    it('should match tracker calculations with direct formula calls', () => {
      // Set up tracker with known portfolio value
      tracker.addPortfolioData(2024, 1, 600000, 200000, 150000, 50000); // Total: $1M
      
      // Use tracker's projection method
      const monthsTracker = tracker.projectFuture(3000000, 0.00565, 3000);
      
      // Direct formula calculation
      const monthsFormula = formulas.monthsToTarget(1000000, 3000000, 0.00565, 3000);
      
      expect(monthsTracker).toBeCloseTo(monthsFormula, 0);
    });

    it('should validate dynamic configuration works correctly', () => {
      // Test with custom configuration
      const customTracker = new WealthTracker({
        defaultTarget: 3000000,
        defaultMonthlyContributions: [2000, 3000, 4000, 5000]
      });
      
      // Add portfolio data
      customTracker.addPortfolioData(2024, 1, 720000, 240000, 180000, 60000); // Total: $1.2M
      
      const config = customTracker.getConfig();
      expect(config.defaultTarget).toBe(3000000);
      expect(config.defaultMonthlyContributions).toContain(3000);
    });
  });

  describe('Comprehensive Scenario Testing', () => {
    const scenarios = [
      { portfolio: 800000, target: 1500000, contribution: 2500, expectedMonths: 80 },
      { portfolio: 1200000, target: 2500000, contribution: 4000, expectedMonths: 93 },
      { portfolio: 500000, target: 1000000, contribution: 1500, expectedMonths: 90 },
      { portfolio: 1500000, target: 5000000, contribution: 8000, expectedMonths: 140 },
      { portfolio: 2000000, target: 3000000, contribution: 0, expectedMonths: 72 }, // Growth only
    ];

    scenarios.forEach(({ portfolio, target, contribution, expectedMonths }) => {
      it(`should calculate ${portfolio} → ${target} with $${contribution}/mo in ~${expectedMonths} months`, () => {
        const months = formulas.monthsToTarget(portfolio, target, 0.00565, contribution);
        expect(months).toBeCloseTo(expectedMonths, -1);
      });
    });
  });
});