import * as formulas from '../formulas';

describe('Independent Mathematical Validation', () => {
  /**
   * Manual calculation using compound interest with regular contributions
   * 
   * Formula: FV = PV(1+r)^n + PMT[((1+r)^n - 1)/r]
   * 
   * To solve for n: n = ln[(FV*r + PMT)/(PV*r + PMT)] / ln(1+r)
   */

  describe('Manual Calculations vs Formula Results', () => {
    const calculateMonthsManually = (pv: number, fv: number, pmt: number, r: number): number => {
      // Using logarithmic rearrangement of compound interest formula
      const numerator = Math.log((fv * r + pmt) / (pv * r + pmt));
      const denominator = Math.log(1 + r);
      return Math.ceil(numerator / denominator);
    };

    const calculateFutureValueManually = (pv: number, pmt: number, r: number, n: number): number => {
      if (r === 0) {
        return pv + pmt * n;
      }
      const growthFactor = Math.pow(1 + r, n);
      return pv * growthFactor + pmt * ((growthFactor - 1) / r);
    };

    it('should match manual calculation: $1M → $3M with $3K/month (7% annual)', () => {
      const pv = 1000000;
      const fv = 3000000;
      const pmt = 3000;
      const r = 0.00565; // 7% annual = 0.565% monthly
      
      // My manual calculation
      const manualMonths = calculateMonthsManually(pv, fv, pmt, r);
      const formulaMonths = formulas.monthsToTarget(pv, fv, r, pmt);
      
      console.log(`Manual calculation: ${manualMonths} months`);
      console.log(`Formula result: ${formulaMonths} months`);
      
      expect(formulaMonths).toBeCloseTo(manualMonths, 0);
    });

    it('should match manual calculation: $1.2M → $3M with $3K/month (7% annual)', () => {
      const pv = 1200000;
      const fv = 3000000;
      const pmt = 3000;
      const r = 0.00565;
      
      const manualMonths = calculateMonthsManually(pv, fv, pmt, r);
      const formulaMonths = formulas.monthsToTarget(pv, fv, r, pmt);
      
      console.log(`Manual: ${manualMonths}, Formula: ${formulaMonths}`);
      expect(formulaMonths).toBeCloseTo(manualMonths, 0);
    });

    it('should match manual calculation: $2M → $3M with $2K/month (7% annual)', () => {
      const pv = 2000000;
      const fv = 3000000;
      const pmt = 2000;
      const r = 0.00565;
      
      const manualMonths = calculateMonthsManually(pv, fv, pmt, r);
      const formulaMonths = formulas.monthsToTarget(pv, fv, r, pmt);
      
      console.log(`Manual: ${manualMonths}, Formula: ${formulaMonths}`);
      expect(formulaMonths).toBeCloseTo(manualMonths, 0);
    });

    it('should match manual FV calculation: $500K + $3K/month for 60 months', () => {
      const pv = 500000;
      const pmt = 3000;
      const r = 0.00565;
      const n = 60;
      
      const manualFV = calculateFutureValueManually(pv, pmt, r, n);
      const formulaFV = formulas.futureValueWithContributions(pv, r, pmt, n);
      
      console.log(`Manual FV: $${manualFV.toFixed(2)}`);
      console.log(`Formula FV: $${formulaFV.toFixed(2)}`);
      
      expect(formulaFV).toBeCloseTo(manualFV, 2);
    });

    it('should validate against different return rates', () => {
      const scenarios = [
        { name: 'Conservative (5%)', rate: 0.00407 },
        { name: 'Moderate (7%)', rate: 0.00565 },
        { name: 'Optimistic (10%)', rate: 0.00797 }
      ];

      scenarios.forEach(({ name, rate }) => {
        const pv = 1000000;
        const fv = 3000000;
        const pmt = 3000;
        
        const manualMonths = calculateMonthsManually(pv, fv, pmt, rate);
        const formulaMonths = formulas.monthsToTarget(pv, fv, rate, pmt);
        
        console.log(`${name}: Manual=${manualMonths}, Formula=${formulaMonths}`);
        expect(formulaMonths).toBeCloseTo(manualMonths, 0);
      });
    });
  });

  describe('Step-by-step Manual Validation', () => {
    it('should show detailed calculation breakdown for $1M → $3M scenario', () => {
      const pv = 1000000;
      const fv = 3000000;
      const pmt = 3000;
      const r = 0.00565;
      
      // Step-by-step manual calculation
      console.log('\n=== DETAILED MANUAL CALCULATION ===');
      console.log(`Starting: $${pv.toLocaleString()}`);
      console.log(`Target: $${fv.toLocaleString()}`);
      console.log(`Monthly contribution: $${pmt.toLocaleString()}`);
      console.log(`Monthly rate: ${(r * 100).toFixed(3)}%`);
      
      // Formula: n = ln[(FV*r + PMT)/(PV*r + PMT)] / ln(1+r)
      const numeratorPart1 = fv * r;
      const numeratorPart2 = numeratorPart1 + pmt;
      const denominatorPart1 = pv * r;
      const denominatorPart2 = denominatorPart1 + pmt;
      const ratio = numeratorPart2 / denominatorPart2;
      const logRatio = Math.log(ratio);
      const logGrowth = Math.log(1 + r);
      const months = Math.ceil(logRatio / logGrowth);
      
      console.log(`FV*r = ${fv} * ${r} = ${numeratorPart1}`);
      console.log(`FV*r + PMT = ${numeratorPart1} + ${pmt} = ${numeratorPart2}`);
      console.log(`PV*r = ${pv} * ${r} = ${denominatorPart1}`);
      console.log(`PV*r + PMT = ${denominatorPart1} + ${pmt} = ${denominatorPart2}`);
      console.log(`Ratio = ${numeratorPart2} / ${denominatorPart2} = ${ratio.toFixed(4)}`);
      console.log(`ln(ratio) = ${logRatio.toFixed(4)}`);
      console.log(`ln(1+r) = ${logGrowth.toFixed(6)}`);
      console.log(`n = ${logRatio.toFixed(4)} / ${logGrowth.toFixed(6)} = ${(logRatio/logGrowth).toFixed(2)}`);
      console.log(`Rounded up: ${months} months`);
      
      const formulaResult = formulas.monthsToTarget(pv, fv, r, pmt);
      console.log(`Formula result: ${formulaResult} months`);
      console.log('✅ Manual calculation matches formula!');
      
      expect(formulaResult).toBe(months);
    });
  });

  describe('Edge Case Validation', () => {
    it('should handle zero contribution scenario correctly', () => {
      const pv = 1000000;
      const fv = 2000000;
      const pmt = 0;
      const r = 0.00565;
      
      // For zero contributions: FV = PV(1+r)^n
      // So: n = ln(FV/PV) / ln(1+r)
      const manualMonths = Math.ceil(Math.log(fv / pv) / Math.log(1 + r));
      const formulaMonths = formulas.monthsToTarget(pv, fv, r, pmt);
      
      console.log(`Zero contribution - Manual: ${manualMonths}, Formula: ${formulaMonths}`);
      expect(formulaMonths).toBeCloseTo(manualMonths, 0);
    });

    it('should handle zero interest rate scenario', () => {
      const pv = 500000;
      const fv = 800000;
      const pmt = 3000;
      const r = 0;
      
      // For zero interest: FV = PV + PMT*n
      // So: n = (FV - PV) / PMT
      const manualMonths = Math.ceil((fv - pv) / pmt);
      const formulaMonths = formulas.monthsToTarget(pv, fv, r, pmt);
      
      console.log(`Zero interest - Manual: ${manualMonths}, Formula: ${formulaMonths}`);
      expect(formulaMonths).toBe(manualMonths);
    });
  });
});