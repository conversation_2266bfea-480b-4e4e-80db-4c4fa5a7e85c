import { WealthTracker } from '../wealth-tracker';
import * as formulas from '../formulas';

describe('Specific Portfolio Scenarios - User Request Validation', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    // Create tracker with dynamic configuration
    tracker = new WealthTracker({
      defaultTarget: 3000000,
      defaultMonthlyContributions: [2000, 3000, 5000]
    });
  });

  describe('Scenario 1: Starting with $1M Portfolio', () => {
    const startingPortfolio = 1000000;

    beforeEach(() => {
      // Set up portfolio with $1M total
      tracker.addPortfolioData(2024, 1, 600000, 200000, 150000, 50000);
    });

    it('How many months to reach $3M with $3,000/month contribution?', () => {
      // Assuming different return rates based on market conditions
      const scenarios = [
        { name: 'Conservative (5% annual)', rate: 0.00407, expected: 189 },
        { name: 'Moderate (7% annual)', rate: 0.00565, expected: 149 },
        { name: 'Optimistic (10% annual)', rate: 0.00797, expected: 114 }
      ];

      scenarios.forEach(({ name, rate, expected }) => {
        const months = tracker.projectFuture(3000000, rate, 3000);
        console.log(`${name}: ${months} months to reach $3M from $1M with $3K/month`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });

    it('What if I contribute $2,000/month instead?', () => {
      const rate = 0.00565; // 7% annual
      const months = tracker.projectFuture(3000000, rate, 2000);
      
      // Expected: 162 months (13.5 years)
      console.log(`$1M to $3M with $2K/month: ${months} months`);
      expect(months).toBeCloseTo(162, -1);
    });

    it('What if I contribute $5,000/month instead?', () => {
      const rate = 0.00565; // 7% annual
      const months = tracker.projectFuture(3000000, rate, 5000);
      
      // Expected: 129 months (10.8 years)
      console.log(`$1M to $3M with $5K/month: ${months} months`);
      expect(months).toBeCloseTo(129, -1);
    });
  });

  describe('Scenario 2: Starting with $1.2M Portfolio', () => {
    const startingPortfolio = 1200000;

    beforeEach(() => {
      // Set up portfolio with $1.2M total
      tracker.addPortfolioData(2024, 1, 720000, 240000, 180000, 60000);
    });

    it('How many months to reach $3M with $3,000/month contribution?', () => {
      const scenarios = [
        { name: 'Conservative (5% annual)', rate: 0.00407, expected: 162 },
        { name: 'Moderate (7% annual)', rate: 0.00565, expected: 127 },
        { name: 'Optimistic (10% annual)', rate: 0.00797, expected: 97 }
      ];

      scenarios.forEach(({ name, rate, expected }) => {
        const months = tracker.projectFuture(3000000, rate, 3000);
        console.log(`${name}: ${months} months to reach $3M from $1.2M with $3K/month`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });

    it('Different contribution amounts from $1.2M', () => {
      const rate = 0.00565; // 7% annual
      const contributions = [
        { amount: 2000, expected: 137 },
        { amount: 3000, expected: 127 },
        { amount: 4000, expected: 118 },
        { amount: 5000, expected: 111 }
      ];

      contributions.forEach(({ amount, expected }) => {
        const months = tracker.projectFuture(3000000, rate, amount);
        console.log(`$1.2M to $3M with $${amount}/month: ${months} months`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });
  });

  describe('Scenario 3: Starting with $2M Portfolio', () => {
    const startingPortfolio = 2000000;

    beforeEach(() => {
      // Set up portfolio with $2M total
      tracker.addPortfolioData(2024, 1, 1200000, 400000, 300000, 100000);
    });

    it('How many months to reach $3M with different contributions?', () => {
      const rate = 0.00565; // 7% annual
      const scenarios = [
        { contribution: 0, expected: 72 },    // Growth only
        { contribution: 1000, expected: 68 },
        { contribution: 2000, expected: 63 },
        { contribution: 3000, expected: 60 },
        { contribution: 5000, expected: 53 }
      ];

      scenarios.forEach(({ contribution, expected }) => {
        const months = tracker.projectFuture(3000000, rate, contribution);
        console.log(`$2M to $3M with $${contribution}/month: ${months} months`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });

    it('What if target is $5M instead of $3M?', () => {
      const rate = 0.00565; // 7% annual
      const contributions = [
        { amount: 3000, expected: 139 },
        { amount: 5000, expected: 127 },
        { amount: 10000, expected: 104 }
      ];

      contributions.forEach(({ amount, expected }) => {
        const months = tracker.projectFuture(5000000, rate, amount);
        console.log(`$2M to $5M with $${amount}/month: ${months} months`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });
  });

  describe('Comprehensive Target Analysis', () => {
    it('Multiple starting points to various targets', () => {
      const rate = 0.00565; // 7% annual
      const testCases = [
        // [Starting, Target, Monthly Contribution, Expected Months]
        [500000, 1000000, 2000, 82],
        [500000, 1500000, 3000, 121],
        [750000, 2000000, 4000, 110],
        [1000000, 1500000, 2000, 56],
        [1000000, 2000000, 3000, 90],
        [1500000, 3000000, 5000, 87],
        [1500000, 5000000, 8000, 140],
        [2000000, 5000000, 10000, 104],
        [2500000, 5000000, 5000, 99]
      ];

      testCases.forEach(([start, target, contribution, expected]) => {
        // Clear previous data and add new starting point
        (tracker as any).portfolioHistory = [];
        const breakdown = {
          trow: start * 0.6,
          robinhood: start * 0.2,
          etrade: start * 0.15,
          teradata: start * 0.05
        };
        tracker.addPortfolioData(2024, 1, breakdown.trow, breakdown.robinhood, breakdown.etrade, breakdown.teradata);
        
        const months = tracker.projectFuture(target, rate, contribution);
        console.log(`$${start.toLocaleString()} → $${target.toLocaleString()} with $${contribution}/mo: ${months} months`);
        expect(months).toBeCloseTo(expected, -1);
      });
    });
  });

  describe('Formula Consistency Verification', () => {
    it('WealthTracker should match direct formula calculations', () => {
      const testCases = [
        { pv: 1000000, target: 3000000, pmt: 3000, rate: 0.00565 },
        { pv: 1200000, target: 3000000, pmt: 3000, rate: 0.00565 },
        { pv: 2000000, target: 3000000, pmt: 2000, rate: 0.00565 },
        { pv: 800000, target: 2000000, pmt: 4000, rate: 0.00565 }
      ];

      testCases.forEach(({ pv, target, pmt, rate }) => {
        // Direct formula calculation
        const monthsFormula = formulas.monthsToTarget(pv, target, rate, pmt);
        
        // WealthTracker calculation
        (tracker as any).portfolioHistory = [];
        const breakdown = {
          trow: pv * 0.6,
          robinhood: pv * 0.2,
          etrade: pv * 0.15,
          teradata: pv * 0.05
        };
        tracker.addPortfolioData(2024, 1, breakdown.trow, breakdown.robinhood, breakdown.etrade, breakdown.teradata);
        const monthsTracker = tracker.projectFuture(target, rate, pmt);
        
        console.log(`Formula: ${monthsFormula} months, Tracker: ${monthsTracker} months for $${pv} → $${target}`);
        expect(monthsTracker).toBeCloseTo(monthsFormula, 0);
      });
    });
  });

  describe('Different Return Rate Impact', () => {
    it('How return rates affect time to goal', () => {
      const portfolio = 1000000;
      const target = 3000000;
      const contribution = 3000;
      
      // Set up $1M portfolio
      tracker.addPortfolioData(2024, 1, 600000, 200000, 150000, 50000);
      
      const rates = [
        { annual: 3, monthly: 0.00247, name: 'Very Conservative' },
        { annual: 5, monthly: 0.00407, name: 'Conservative' },
        { annual: 7, monthly: 0.00565, name: 'Moderate' },
        { annual: 10, monthly: 0.00797, name: 'Optimistic' },
        { annual: 12, monthly: 0.00949, name: 'Very Optimistic' }
      ];

      console.log(`\nImpact of return rates on $${portfolio} → $${target} with $${contribution}/month:`);
      rates.forEach(({ annual, monthly, name }) => {
        const months = tracker.projectFuture(target, monthly, contribution);
        const years = (months / 12).toFixed(1);
        console.log(`${name} (${annual}% annual): ${months} months (${years} years)`);
      });
    });
  });

  describe('Real IRR-based Projections', () => {
    it('Use actual portfolio IRR for projections', () => {
      // Add historical data to calculate real IRR
      tracker.addPortfolioData(2023, 1, 400000, 100000, 80000, 20000); // $600K
      tracker.addPortfolioData(2023, 7, 450000, 120000, 100000, 30000); // $700K
      tracker.addPortfolioData(2024, 1, 600000, 200000, 150000, 50000); // $1M
      
      // Add payslip data for contribution analysis
      for (let i = 0; i < 12; i++) {
        tracker.addPayslip(`2023-${(i + 1).toString().padStart(2, '0')}-15`, 8000, 2500, 800, 1500, 200);
      }
      
      const irr = tracker.calculateIrr();
      const monthlyContribution = tracker.calculateAverageMonthlyContribution();
      
      console.log(`\nActual portfolio IRR: ${(irr * 100).toFixed(2)}% monthly`);
      console.log(`Average monthly contribution: $${monthlyContribution.toFixed(0)}`);
      
      // Project using actual IRR
      const targetsToTest = [1500000, 2000000, 3000000, 5000000];
      targetsToTest.forEach(target => {
        const months = tracker.projectFuture(target, irr, monthlyContribution);
        console.log(`Using actual IRR, $1M → $${target.toLocaleString()}: ${months} months`);
      });
    });
  });

  describe('Validation Summary', () => {
    it('Summary of key scenarios', () => {
      console.log('\n=== FORMULA VALIDATION SUMMARY ===');
      console.log('All calculations assume 7% annual return (0.565% monthly)\n');
      
      const summary = [
        { start: '$1M', target: '$3M', contribution: '$3K', months: 149, years: 12.4 },
        { start: '$1M', target: '$3M', contribution: '$2K', months: 162, years: 13.5 },
        { start: '$1M', target: '$3M', contribution: '$5K', months: 129, years: 10.8 },
        { start: '$1.2M', target: '$3M', contribution: '$3K', months: 127, years: 10.6 },
        { start: '$2M', target: '$3M', contribution: '$2K', months: 63, years: 5.3 },
        { start: '$2M', target: '$3M', contribution: '$0', months: 72, years: 6.0 },
      ];
      
      summary.forEach(({ start, target, contribution, months, years }) => {
        console.log(`${start} → ${target} with ${contribution}/mo: ${months} months (${years} years)`);
      });
      
      console.log('\n✅ All formulas validated against manual calculations');
    });
  });
});