export interface PortfolioEntry {
  date: string;
  trow: number;
  robinhood: number;
  etrade: number;
  teradata: number;
  fidelity?: number;
}

export interface PayslipEntry {
  date: string;
  gross: number;
  net: number;
  espp: number;
  roth_e: number;
  roth_r: number;
  total_invest: number;
}

export interface WealthData {
  portfolio_history: PortfolioEntry[];
  payslip_history: PayslipEntry[];
}

export interface ScenarioRates {
  [key: string]: number;
}

export interface Statistics {
  meanReturn: number;
  stdDev: number;
}

// Dynamic Configuration Interface
export interface WealthTrackerConfig {
  // Target Configuration
  wealthTargets: number[];  // e.g., [900000, 1500000, 2000000, 3000000]
  defaultTarget: number;    // e.g., 1500000
  
  // Investment Configuration
  defaultMonthlyContributions: number[];  // e.g., [3000, 4000, 5000, 6250]
  fallbackMonthlyContribution: number;    // e.g., 4000 instead of hardcoded 6248
  
  // Bonus Configuration
  annualBonusGross: number;
  annualBonusNet: number;
  bonusMonth: number;  // 1-12
  
  // Pay Configuration
  paychecksPerYear: number;  // 26 for bi-weekly, 24 for semi-monthly, 12 for monthly
  
  // Expense Configuration
  monthlyExpenses: number;
  
  // Scenario Configuration
  customScenarioRates?: {
    conservative?: number;  // Monthly rate, e.g., 0.005
    baseline?: number;      // Will use calculated IRR if not provided
    optimistic?: number;    // Will use mean return if not provided
    custom?: Array<{ name: string; rate: number; description: string }>;
  };
  
  // Analysis Configuration
  analysisTimeframes: number[];  // e.g., [12, 24, 36, 48] months for projections
  coastFireRetirementAge: number;  // e.g., 65
  coastFireWithdrawalRate: number;  // e.g., 0.04 for 4% rule
}

// Default configuration
export const DEFAULT_CONFIG: WealthTrackerConfig = {
  wealthTargets: [900000, 1500000, 2000000, 3000000],
  defaultTarget: 1500000,
  defaultMonthlyContributions: [3000, 4000, 5000, 6250],
  fallbackMonthlyContribution: 4000,
  annualBonusGross: 44792.41,
  annualBonusNet: 26391.69,
  bonusMonth: 3,
  paychecksPerYear: 26,
  monthlyExpenses: 2800,
  analysisTimeframes: [12, 24, 36],
  coastFireRetirementAge: 65,
  coastFireWithdrawalRate: 0.04
};