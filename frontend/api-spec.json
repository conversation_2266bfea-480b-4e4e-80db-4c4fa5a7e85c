{"endpoints": [{"path": "/api/portfolio", "method": "GET", "description": "Get all portfolio entries", "response": {"type": "array", "items": {"date": "string", "trow": "number", "robinhood": "number", "etrade": "number", "teradata": "number", "fidelity": "number", "total": "number"}}}, {"path": "/api/portfolio/recent", "method": "GET", "description": "Get most recent portfolio entry", "response": {"date": "string", "trow": "number", "robinhood": "number", "etrade": "number", "teradata": "number", "fidelity": "number", "total": "number"}}, {"path": "/api/portfolio", "method": "POST", "description": "Add new portfolio entry", "request": {"year": "number", "month": "number", "trow": "number?", "robinhood": "number?", "etrade": "number?", "teradata": "number?", "fidelity": "number?"}, "response": {"success": "boolean", "message": "string", "data": "PortfolioEntry?"}}, {"path": "/api/payslips", "method": "GET", "description": "Get all payslips", "response": {"type": "array", "items": {"date": "string", "gross": "number", "net": "number", "espp": "number", "roth_e": "number", "roth_r": "number"}}}, {"path": "/api/payslips", "method": "POST", "description": "Add new payslip", "request": {"date": "string", "gross": "number", "net": "number", "espp": "number", "roth_e": "number", "roth_r": "number"}, "response": {"success": "boolean", "message": "string"}}, {"path": "/api/payslips/parse", "method": "POST", "description": "Parse payslip text", "request": {"payslipTexts": "string[]"}, "response": {"success": "boolean", "parsedPayslips": "PayslipEntry[]", "errors": "string[]?"}}, {"path": "/api/analysis", "method": "GET", "description": "Get wealth analysis and projections", "response": {"currentWealth": "number", "projections": {"years": "number[]", "values": "number[]"}, "analytics": {"savingsRate": "number", "monthlyContributions": "number", "yearlyGrowth": "number", "timeToMillionaire": "number"}}}, {"path": "/api/analysis/validate", "method": "GET", "description": "Check if sufficient data exists for analysis", "response": {"valid": "boolean", "message": "string"}}]}