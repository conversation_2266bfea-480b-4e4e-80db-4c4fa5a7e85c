{"/Users/<USER>/WebstormProjects/er/frontend/src/App.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/App.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 60}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 63}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 71}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 61}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 67}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 57}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 53}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 51}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 52}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 38}}, "11": {"start": {"line": 13, "column": 22}, "end": {"line": 249, "column": 1}}, "12": {"start": {"line": 14, "column": 36}, "end": {"line": 14, "column": 93}}, "13": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 24}}, "14": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 23}}, "15": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 21}}, "16": {"start": {"line": 32, "column": 30}, "end": {"line": 37, "column": 3}}, "17": {"start": {"line": 33, "column": 20}, "end": {"line": 33, "column": 41}}, "18": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 36}}, "20": {"start": {"line": 39, "column": 27}, "end": {"line": 42, "column": 3}}, "21": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 27}}, "22": {"start": {"line": 45, "column": 29}, "end": {"line": 70, "column": 4}}, "23": {"start": {"line": 72, "column": 2}, "end": {"line": 248, "column": 4}}, "24": {"start": {"line": 102, "column": 29}, "end": {"line": 102, "column": 54}}, "25": {"start": {"line": 109, "column": 29}, "end": {"line": 109, "column": 52}}, "26": {"start": {"line": 116, "column": 29}, "end": {"line": 116, "column": 51}}, "27": {"start": {"line": 203, "column": 45}, "end": {"line": 203, "column": 81}}, "28": {"start": {"line": 204, "column": 36}, "end": {"line": 204, "column": 65}}, "29": {"start": {"line": 226, "column": 39}, "end": {"line": 226, "column": 64}}, "30": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 25}}, "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 249, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 12}, "end": {"line": 28, "column": 15}}, "loc": {"start": {"line": 28, "column": 17}, "end": {"line": 30, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 35}}, "loc": {"start": {"line": 32, "column": 41}, "end": {"line": 37, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 27}, "end": {"line": 39, "column": 28}}, "loc": {"start": {"line": 39, "column": 47}, "end": {"line": 42, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 26}}, "loc": {"start": {"line": 102, "column": 29}, "end": {"line": 102, "column": 54}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 109, "column": 23}, "end": {"line": 109, "column": 26}}, "loc": {"start": {"line": 109, "column": 29}, "end": {"line": 109, "column": 52}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 116, "column": 23}, "end": {"line": 116, "column": 26}}, "loc": {"start": {"line": 116, "column": 29}, "end": {"line": 116, "column": 51}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 203, "column": 34}, "end": {"line": 203, "column": 35}}, "loc": {"start": {"line": 203, "column": 45}, "end": {"line": 203, "column": 81}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 204, "column": 30}, "end": {"line": 204, "column": 33}}, "loc": {"start": {"line": 204, "column": 36}, "end": {"line": 204, "column": 65}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 226, "column": 33}, "end": {"line": 226, "column": 36}}, "loc": {"start": {"line": 226, "column": 39}, "end": {"line": 226, "column": 64}}}}, "branchMap": {"0": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}]}, "1": {"loc": {"start": {"line": 101, "column": 45}, "end": {"line": 101, "column": 91}}, "type": "cond-expr", "locations": [{"start": {"line": 101, "column": 73}, "end": {"line": 101, "column": 86}}, {"start": {"line": 101, "column": 89}, "end": {"line": 101, "column": 91}}]}, "2": {"loc": {"start": {"line": 108, "column": 45}, "end": {"line": 108, "column": 89}}, "type": "cond-expr", "locations": [{"start": {"line": 108, "column": 71}, "end": {"line": 108, "column": 84}}, {"start": {"line": 108, "column": 87}, "end": {"line": 108, "column": 89}}]}, "3": {"loc": {"start": {"line": 115, "column": 45}, "end": {"line": 115, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 70}, "end": {"line": 115, "column": 83}}, {"start": {"line": 115, "column": 86}, "end": {"line": 115, "column": 88}}]}, "4": {"loc": {"start": {"line": 125, "column": 11}, "end": {"line": 130, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 11}, "end": {"line": 125, "column": 16}}, {"start": {"line": 126, "column": 12}, "end": {"line": 129, "column": null}}]}, "5": {"loc": {"start": {"line": 133, "column": 11}, "end": {"line": 234, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 71}}, {"start": {"line": 136, "column": 12}, "end": {"line": 233, "column": null}}]}, "6": {"loc": {"start": {"line": 137, "column": 15}, "end": {"line": 196, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 137, "column": 15}, "end": {"line": 137, "column": 40}}, {"start": {"line": 138, "column": 16}, "end": {"line": 195, "column": null}}]}, "7": {"loc": {"start": {"line": 145, "column": 19}, "end": {"line": 148, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 19}, "end": {"line": 145, "column": 27}}, {"start": {"line": 146, "column": 20}, "end": {"line": 147, "column": null}}]}, "8": {"loc": {"start": {"line": 152, "column": 19}, "end": {"line": 158, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 19}, "end": {"line": 152, "column": 30}}, {"start": {"line": 152, "column": 34}, "end": {"line": 152, "column": 56}}, {"start": {"line": 153, "column": 20}, "end": {"line": 157, "column": null}}]}, "9": {"loc": {"start": {"line": 162, "column": 19}, "end": {"line": 194, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 162, "column": 19}, "end": {"line": 162, "column": 29}}, {"start": {"line": 163, "column": 20}, "end": {"line": 193, "column": null}}]}, "10": {"loc": {"start": {"line": 199, "column": 15}, "end": {"line": 206, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 199, "column": 15}, "end": {"line": 199, "column": 38}}, {"start": {"line": 200, "column": 16}, "end": {"line": 205, "column": null}}]}, "11": {"loc": {"start": {"line": 209, "column": 15}, "end": {"line": 232, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 15}, "end": {"line": 209, "column": 37}}, {"start": {"line": 210, "column": 16}, "end": {"line": 231, "column": null}}]}, "12": {"loc": {"start": {"line": 211, "column": 19}, "end": {"line": 230, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 212, "column": 20}, "end": {"line": 216, "column": null}}, {"start": {"line": 220, "column": 20}, "end": {"line": 229, "column": null}}]}, "13": {"loc": {"start": {"line": 211, "column": 19}, "end": {"line": 211, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 211, "column": 19}, "end": {"line": 211, "column": 30}}, {"start": {"line": 211, "column": 34}, "end": {"line": 211, "column": 56}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 10, "13": 9, "14": 9, "15": 9, "16": 9, "17": 0, "18": 0, "19": 0, "20": 9, "21": 0, "22": 9, "23": 9, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 1}, "f": {"0": 10, "1": 9, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [9, 0], "2": [0, 9], "3": [0, 9], "4": [9, 1], "5": [1, 8], "6": [8, 8], "7": [8, 1], "8": [8, 0, 0], "9": [8, 2], "10": [8, 0], "11": [8, 0], "12": [0, 0], "13": [0, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/CalculatorPage.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/CalculatorPage.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 40}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 46}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 52}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "6": {"start": {"line": 33, "column": 33}, "end": {"line": 396, "column": 1}}, "7": {"start": {"line": 34, "column": 44}, "end": {"line": 34, "column": 59}}, "8": {"start": {"line": 35, "column": 30}, "end": {"line": 35, "column": 70}}, "9": {"start": {"line": 36, "column": 54}, "end": {"line": 36, "column": 88}}, "10": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 48}}, "11": {"start": {"line": 39, "column": 77}, "end": {"line": 47, "column": 4}}, "12": {"start": {"line": 50, "column": 24}, "end": {"line": 50, "column": 31}}, "13": {"start": {"line": 53, "column": 2}, "end": {"line": 66, "column": 9}}, "14": {"start": {"line": 54, "column": 25}, "end": {"line": 54, "column": 67}}, "15": {"start": {"line": 55, "column": 4}, "end": {"line": 65, "column": 5}}, "16": {"start": {"line": 56, "column": 6}, "end": {"line": 64, "column": 7}}, "17": {"start": {"line": 57, "column": 23}, "end": {"line": 57, "column": 47}}, "18": {"start": {"line": 58, "column": 8}, "end": {"line": 61, "column": 13}}, "19": {"start": {"line": 58, "column": 57}, "end": {"line": 61, "column": 10}}, "20": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 69}}, "21": {"start": {"line": 69, "column": 33}, "end": {"line": 72, "column": 3}}, "22": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 38}}, "23": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 75}}, "24": {"start": {"line": 75, "column": 2}, "end": {"line": 86, "column": 22}}, "25": {"start": {"line": 76, "column": 26}, "end": {"line": 83, "column": 11}}, "26": {"start": {"line": 77, "column": 6}, "end": {"line": 82, "column": 7}}, "27": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 49}}, "28": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 45}}, "29": {"start": {"line": 85, "column": 17}, "end": {"line": 85, "column": 44}}, "30": {"start": {"line": 88, "column": 29}, "end": {"line": 151, "column": 3}}, "31": {"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}, "32": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 29}}, "33": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 17}}, "34": {"start": {"line": 94, "column": 4}, "end": {"line": 150, "column": 5}}, "35": {"start": {"line": 96, "column": 26}, "end": {"line": 96, "column": 60}}, "36": {"start": {"line": 99, "column": 23}, "end": {"line": 104, "column": 8}}, "37": {"start": {"line": 106, "column": 6}, "end": {"line": 108, "column": 7}}, "38": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 59}}, "39": {"start": {"line": 111, "column": 31}, "end": {"line": 111, "column": 93}}, "40": {"start": {"line": 111, "column": 62}, "end": {"line": 111, "column": 92}}, "41": {"start": {"line": 112, "column": 29}, "end": {"line": 112, "column": 74}}, "42": {"start": {"line": 115, "column": 33}, "end": {"line": 115, "column": 76}}, "43": {"start": {"line": 116, "column": 26}, "end": {"line": 116, "column": 92}}, "44": {"start": {"line": 117, "column": 32}, "end": {"line": 117, "column": 100}}, "45": {"start": {"line": 118, "column": 26}, "end": {"line": 118, "column": 87}}, "46": {"start": {"line": 120, "column": 51}, "end": {"line": 128, "column": 8}}, "47": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 35}}, "48": {"start": {"line": 133, "column": 6}, "end": {"line": 143, "column": 7}}, "49": {"start": {"line": 134, "column": 51}, "end": {"line": 139, "column": 10}}, "50": {"start": {"line": 141, "column": 31}, "end": {"line": 141, "column": 82}}, "51": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 47}}, "52": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 77}}, "53": {"start": {"line": 147, "column": 6}, "end": {"line": 147, "column": 49}}, "54": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 30}}, "55": {"start": {"line": 153, "column": 19}, "end": {"line": 155, "column": 3}}, "56": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 41}}, "57": {"start": {"line": 157, "column": 23}, "end": {"line": 160, "column": 3}}, "58": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 30}}, "59": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 50}}, "60": {"start": {"line": 162, "column": 26}, "end": {"line": 169, "column": 3}}, "61": {"start": {"line": 163, "column": 4}, "end": {"line": 163, "column": 70}}, "62": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 62}}, "63": {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": 76}}, "64": {"start": {"line": 166, "column": 4}, "end": {"line": 166, "column": 70}}, "65": {"start": {"line": 167, "column": 4}, "end": {"line": 167, "column": 70}}, "66": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 35}}, "67": {"start": {"line": 171, "column": 2}, "end": {"line": 395, "column": 4}}, "68": {"start": {"line": 363, "column": 20}, "end": {"line": 364, "column": null}}, "69": {"start": {"line": 366, "column": 37}, "end": {"line": 366, "column": 58}}, "70": {"start": {"line": 398, "column": 0}, "end": {"line": 398, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 33}, "end": {"line": 33, "column": 36}}, "loc": {"start": {"line": 33, "column": 38}, "end": {"line": 396, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 15}}, "loc": {"start": {"line": 53, "column": 17}, "end": {"line": 66, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 58, "column": 41}, "end": {"line": 58, "column": 42}}, "loc": {"start": {"line": 58, "column": 57}, "end": {"line": 61, "column": 10}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 69, "column": 33}, "end": {"line": 69, "column": 34}}, "loc": {"start": {"line": 69, "column": 70}, "end": {"line": 72, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 15}}, "loc": {"start": {"line": 75, "column": 17}, "end": {"line": 86, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 76, "column": 37}, "end": {"line": 76, "column": 40}}, "loc": {"start": {"line": 76, "column": 42}, "end": {"line": 83, "column": 5}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 85, "column": 11}, "end": {"line": 85, "column": 14}}, "loc": {"start": {"line": 85, "column": 17}, "end": {"line": 85, "column": 44}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 88, "column": 29}, "end": {"line": 88, "column": 34}}, "loc": {"start": {"line": 88, "column": 95}, "end": {"line": 151, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 111, "column": 50}, "end": {"line": 111, "column": 51}}, "loc": {"start": {"line": 111, "column": 62}, "end": {"line": 111, "column": 92}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 153, "column": 19}, "end": {"line": 153, "column": 24}}, "loc": {"start": {"line": 153, "column": 52}, "end": {"line": 155, "column": 3}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 157, "column": 23}, "end": {"line": 157, "column": 26}}, "loc": {"start": {"line": 157, "column": 28}, "end": {"line": 160, "column": 3}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 162, "column": 26}, "end": {"line": 162, "column": 27}}, "loc": {"start": {"line": 162, "column": 62}, "end": {"line": 169, "column": 3}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 362, "column": 42}, "end": {"line": 362, "column": 43}}, "loc": {"start": {"line": 363, "column": 20}, "end": {"line": 364, "column": null}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 366, "column": 31}, "end": {"line": 366, "column": 34}}, "loc": {"start": {"line": 366, "column": 37}, "end": {"line": 366, "column": 58}}}}, "branchMap": {"0": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 65, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 65, "column": 5}}]}, "1": {"loc": {"start": {"line": 77, "column": 6}, "end": {"line": 82, "column": 7}}, "type": "if", "locations": [{"start": {"line": 77, "column": 6}, "end": {"line": 82, "column": 7}}]}, "2": {"loc": {"start": {"line": 77, "column": 10}, "end": {"line": 80, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": 40}}, {"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 36}}, {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 43}}, {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 40}}]}, "3": {"loc": {"start": {"line": 88, "column": 62}, "end": {"line": 88, "column": 91}}, "type": "default-arg", "locations": [{"start": {"line": 88, "column": 87}, "end": {"line": 88, "column": 91}}]}, "4": {"loc": {"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}, "type": "if", "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}]}, "5": {"loc": {"start": {"line": 103, "column": 15}, "end": {"line": 103, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 15}, "end": {"line": 103, "column": 38}}, {"start": {"line": 103, "column": 42}, "end": {"line": 103, "column": 44}}]}, "6": {"loc": {"start": {"line": 106, "column": 6}, "end": {"line": 108, "column": 7}}, "type": "if", "locations": [{"start": {"line": 106, "column": 6}, "end": {"line": 108, "column": 7}}]}, "7": {"loc": {"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 27}}, {"start": {"line": 106, "column": 31}, "end": {"line": 106, "column": 45}}]}, "8": {"loc": {"start": {"line": 112, "column": 29}, "end": {"line": 112, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 48}, "end": {"line": 112, "column": 70}}, {"start": {"line": 112, "column": 73}, "end": {"line": 112, "column": 74}}]}, "9": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 143, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 143, "column": 7}}]}, "10": {"loc": {"start": {"line": 209, "column": 31}, "end": {"line": 209, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 57}, "end": {"line": 209, "column": 74}}, {"start": {"line": 209, "column": 77}, "end": {"line": 209, "column": 79}}]}, "11": {"loc": {"start": {"line": 212, "column": 19}, "end": {"line": 212, "column": 121}}, "type": "binary-expr", "locations": [{"start": {"line": 212, "column": 19}, "end": {"line": 212, "column": 42}}, {"start": {"line": 212, "column": 46}, "end": {"line": 212, "column": 121}}]}, "12": {"loc": {"start": {"line": 230, "column": 31}, "end": {"line": 230, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 230, "column": 53}, "end": {"line": 230, "column": 70}}, {"start": {"line": 230, "column": 73}, "end": {"line": 230, "column": 75}}]}, "13": {"loc": {"start": {"line": 233, "column": 19}, "end": {"line": 233, "column": 113}}, "type": "binary-expr", "locations": [{"start": {"line": 233, "column": 19}, "end": {"line": 233, "column": 38}}, {"start": {"line": 233, "column": 42}, "end": {"line": 233, "column": 113}}]}, "14": {"loc": {"start": {"line": 251, "column": 31}, "end": {"line": 251, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 251, "column": 60}, "end": {"line": 251, "column": 77}}, {"start": {"line": 251, "column": 80}, "end": {"line": 251, "column": 82}}]}, "15": {"loc": {"start": {"line": 254, "column": 19}, "end": {"line": 254, "column": 127}}, "type": "binary-expr", "locations": [{"start": {"line": 254, "column": 19}, "end": {"line": 254, "column": 45}}, {"start": {"line": 254, "column": 49}, "end": {"line": 254, "column": 127}}]}, "16": {"loc": {"start": {"line": 275, "column": 31}, "end": {"line": 275, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 57}, "end": {"line": 275, "column": 74}}, {"start": {"line": 275, "column": 77}, "end": {"line": 275, "column": 79}}]}, "17": {"loc": {"start": {"line": 278, "column": 19}, "end": {"line": 278, "column": 121}}, "type": "binary-expr", "locations": [{"start": {"line": 278, "column": 19}, "end": {"line": 278, "column": 42}}, {"start": {"line": 278, "column": 46}, "end": {"line": 278, "column": 121}}]}, "18": {"loc": {"start": {"line": 287, "column": 19}, "end": {"line": 287, "column": 90}}, "type": "cond-expr", "locations": [{"start": {"line": 287, "column": 35}, "end": {"line": 287, "column": 66}}, {"start": {"line": 287, "column": 69}, "end": {"line": 287, "column": 90}}]}, "19": {"loc": {"start": {"line": 299, "column": 15}, "end": {"line": 299, "column": 102}}, "type": "binary-expr", "locations": [{"start": {"line": 299, "column": 15}, "end": {"line": 299, "column": 20}}, {"start": {"line": 299, "column": 24}, "end": {"line": 299, "column": 102}}]}, "20": {"loc": {"start": {"line": 301, "column": 15}, "end": {"line": 337, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 302, "column": 16}, "end": {"line": 331, "column": null}}, {"start": {"line": 334, "column": 16}, "end": {"line": 336, "column": null}}]}, "21": {"loc": {"start": {"line": 349, "column": 17}, "end": {"line": 356, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 349, "column": 17}, "end": {"line": 349, "column": 46}}, {"start": {"line": 350, "column": 18}, "end": {"line": 353, "column": null}}]}, "22": {"loc": {"start": {"line": 360, "column": 15}, "end": {"line": 388, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 361, "column": 16}, "end": {"line": 382, "column": null}}, {"start": {"line": 385, "column": 16}, "end": {"line": 387, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 50, "8": 50, "9": 50, "10": 50, "11": 50, "12": 50, "13": 50, "14": 14, "15": 14, "16": 3, "17": 3, "18": 3, "19": 3, "20": 0, "21": 50, "22": 4, "23": 4, "24": 50, "25": 50, "26": 1, "27": 0, "28": 50, "29": 50, "30": 50, "31": 5, "32": 5, "33": 5, "34": 5, "35": 5, "36": 5, "37": 5, "38": 1, "39": 4, "40": 9, "41": 4, "42": 4, "43": 4, "44": 4, "45": 4, "46": 4, "47": 4, "48": 4, "49": 4, "50": 4, "51": 4, "52": 1, "53": 1, "54": 5, "55": 50, "56": 5, "57": 50, "58": 1, "59": 1, "60": 50, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 50, "68": 8, "69": 1, "70": 1}, "f": {"0": 50, "1": 14, "2": 3, "3": 4, "4": 50, "5": 1, "6": 50, "7": 5, "8": 9, "9": 5, "10": 1, "11": 1, "12": 8, "13": 1}, "b": {"0": [3], "1": [0], "2": [1, 0, 0, 0], "3": [0], "4": [5], "5": [5, 0], "6": [1], "7": [5, 4], "8": [3, 1], "9": [4], "10": [1, 49], "11": [50, 1], "12": [0, 50], "13": [50, 0], "14": [0, 50], "15": [50, 0], "16": [0, 50], "17": [50, 0], "18": [1, 49], "19": [50, 1], "20": [5, 45], "21": [50, 8], "22": [8, 42]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/ErrorMessage.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/ErrorMessage.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 41}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 11, "column": 50}, "end": {"line": 47, "column": 1}}, "3": {"start": {"line": 17, "column": 2}, "end": {"line": 25, "column": 51}}, "4": {"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}, "5": {"start": {"line": 19, "column": 20}, "end": {"line": 21, "column": 28}}, "6": {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 20}}, "7": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 39}}, "8": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 38}}, "9": {"start": {"line": 27, "column": 2}, "end": {"line": 46, "column": 4}}, "10": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 11, "column": 50}, "end": {"line": 11, "column": 51}}, "loc": {"start": {"line": 16, "column": 5}, "end": {"line": 47, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 15}}, "loc": {"start": {"line": 17, "column": 17}, "end": {"line": 25, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 19, "column": 31}, "end": {"line": 19, "column": 34}}, "loc": {"start": {"line": 19, "column": 36}, "end": {"line": 21, "column": 7}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 16}}, "loc": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 38}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 21}}]}, "1": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 23}, "end": {"line": 15, "column": 27}}]}, "2": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 5, "4": 5, "5": 1, "6": 1, "7": 1, "8": 1, "9": 5, "10": 1}, "f": {"0": 5, "1": 5, "2": 1, "3": 1}, "b": {"0": [4], "1": [4], "2": [1]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoricalDataTable.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoricalDataTable.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 70}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 40}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 53}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 54}}, "5": {"start": {"line": 23, "column": 64}, "end": {"line": 360, "column": 1}}, "6": {"start": {"line": 28, "column": 38}, "end": {"line": 28, "column": 99}}, "7": {"start": {"line": 29, "column": 36}, "end": {"line": 29, "column": 88}}, "8": {"start": {"line": 31, "column": 25}, "end": {"line": 38, "column": 3}}, "9": {"start": {"line": 32, "column": 4}, "end": {"line": 37, "column": 22}}, "10": {"start": {"line": 40, "column": 21}, "end": {"line": 48, "column": 3}}, "11": {"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 20}}, "12": {"start": {"line": 50, "column": 22}, "end": {"line": 57, "column": 3}}, "13": {"start": {"line": 51, "column": 4}, "end": {"line": 56, "column": 5}}, "14": {"start": {"line": 52, "column": 24}, "end": {"line": 52, "column": 36}}, "15": {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 34}}, "16": {"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 35}}, "17": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 27}}, "18": {"start": {"line": 59, "column": 23}, "end": {"line": 66, "column": 3}}, "19": {"start": {"line": 60, "column": 4}, "end": {"line": 65, "column": 5}}, "20": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 41}}, "21": {"start": {"line": 62, "column": 22}, "end": {"line": 62, "column": 39}}, "22": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 40}}, "23": {"start": {"line": 64, "column": 15}, "end": {"line": 64, "column": 32}}, "24": {"start": {"line": 68, "column": 23}, "end": {"line": 91, "column": 38}}, "25": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 26}}, "26": {"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}, "27": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 69}}, "28": {"start": {"line": 73, "column": 42}, "end": {"line": 73, "column": 67}}, "29": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "30": {"start": {"line": 78, "column": 18}, "end": {"line": 78, "column": 28}}, "31": {"start": {"line": 79, "column": 19}, "end": {"line": 84, "column": 23}}, "32": {"start": {"line": 86, "column": 25}, "end": {"line": 86, "column": 77}}, "33": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 68}}, "34": {"start": {"line": 87, "column": 42}, "end": {"line": 87, "column": 66}}, "35": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 20}}, "36": {"start": {"line": 93, "column": 45}, "end": {"line": 175, "column": null}}, "37": {"start": {"line": 94, "column": 10}, "end": {"line": 174, "column": null}}, "38": {"start": {"line": 97, "column": 10}, "end": {"line": 101, "column": null}}, "39": {"start": {"line": 106, "column": 10}, "end": {"line": 107, "column": null}}, "40": {"start": {"line": 116, "column": 10}, "end": {"line": 125, "column": null}}, "41": {"start": {"line": 133, "column": 10}, "end": {"line": 133, "column": 61}}, "42": {"start": {"line": 138, "column": 10}, "end": {"line": 142, "column": null}}, "43": {"start": {"line": 147, "column": 10}, "end": {"line": 148, "column": null}}, "44": {"start": {"line": 158, "column": 10}, "end": {"line": 170, "column": null}}, "45": {"start": {"line": 165, "column": 31}, "end": {"line": 165, "column": 59}}, "46": {"start": {"line": 184, "column": 6}, "end": {"line": 193, "column": null}}, "47": {"start": {"line": 196, "column": 26}, "end": {"line": 204, "column": 3}}, "48": {"start": {"line": 197, "column": 19}, "end": {"line": 202, "column": 6}}, "49": {"start": {"line": 199, "column": 37}, "end": {"line": 199, "column": 59}}, "50": {"start": {"line": 200, "column": 35}, "end": {"line": 200, "column": 55}}, "51": {"start": {"line": 201, "column": 36}, "end": {"line": 201, "column": 57}}, "52": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 18}}, "53": {"start": {"line": 206, "column": 23}, "end": {"line": 206, "column": 40}}, "54": {"start": {"line": 208, "column": 2}, "end": {"line": 359, "column": 4}}, "55": {"start": {"line": 241, "column": 18}, "end": {"line": 242, "column": null}}, "56": {"start": {"line": 244, "column": 35}, "end": {"line": 244, "column": 61}}, "57": {"start": {"line": 259, "column": 33}, "end": {"line": 259, "column": 68}}, "58": {"start": {"line": 281, "column": 18}, "end": {"line": 281, "column": 39}}, "59": {"start": {"line": 282, "column": 18}, "end": {"line": 282, "column": 38}}, "60": {"start": {"line": 292, "column": 59}, "end": {"line": 292, "column": 92}}, "61": {"start": {"line": 293, "column": 18}, "end": {"line": 317, "column": 20}}, "62": {"start": {"line": 296, "column": 60}, "end": {"line": 297, "column": null}}, "63": {"start": {"line": 299, "column": 24}, "end": {"line": 314, "column": 26}}, "64": {"start": {"line": 322, "column": 18}, "end": {"line": 322, "column": 34}}, "65": {"start": {"line": 323, "column": 51}, "end": {"line": 323, "column": 68}}, "66": {"start": {"line": 324, "column": 18}, "end": {"line": 340, "column": 20}}, "67": {"start": {"line": 329, "column": 37}, "end": {"line": 329, "column": 65}}, "68": {"start": {"line": 332, "column": 58}, "end": {"line": 332, "column": 77}}, "69": {"start": {"line": 333, "column": 24}, "end": {"line": 337, "column": 26}}, "70": {"start": {"line": 362, "column": 0}, "end": {"line": 362, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 64}, "end": {"line": 23, "column": 65}}, "loc": {"start": {"line": 27, "column": 5}, "end": {"line": 360, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 26}}, "loc": {"start": {"line": 31, "column": 44}, "end": {"line": 38, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 22}}, "loc": {"start": {"line": 40, "column": 36}, "end": {"line": 48, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": 23}}, "loc": {"start": {"line": 50, "column": 39}, "end": {"line": 57, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 59, "column": 23}, "end": {"line": 59, "column": 24}}, "loc": {"start": {"line": 59, "column": 40}, "end": {"line": 66, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 68, "column": 31}, "end": {"line": 68, "column": 34}}, "loc": {"start": {"line": 68, "column": 36}, "end": {"line": 91, "column": 3}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 73, "column": 33}, "end": {"line": 73, "column": 38}}, "loc": {"start": {"line": 73, "column": 42}, "end": {"line": 73, "column": 67}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 87, "column": 33}, "end": {"line": 87, "column": 38}}, "loc": {"start": {"line": 87, "column": 42}, "end": {"line": 87, "column": 66}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 7}}, "loc": {"start": {"line": 94, "column": 10}, "end": {"line": 174, "column": null}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 96, "column": 16}, "end": {"line": 96, "column": 19}}, "loc": {"start": {"line": 97, "column": 10}, "end": {"line": 101, "column": null}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 105, "column": 14}, "end": {"line": 105, "column": 15}}, "loc": {"start": {"line": 106, "column": 10}, "end": {"line": 107, "column": null}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 15}}, "loc": {"start": {"line": 116, "column": 10}, "end": {"line": 125, "column": null}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 132, "column": 14}, "end": {"line": 132, "column": 15}}, "loc": {"start": {"line": 133, "column": 10}, "end": {"line": 133, "column": 61}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 137, "column": 16}, "end": {"line": 137, "column": 19}}, "loc": {"start": {"line": 138, "column": 10}, "end": {"line": 142, "column": null}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 146, "column": 14}, "end": {"line": 146, "column": 15}}, "loc": {"start": {"line": 147, "column": 10}, "end": {"line": 148, "column": null}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 157, "column": 14}, "end": {"line": 157, "column": 15}}, "loc": {"start": {"line": 158, "column": 10}, "end": {"line": 170, "column": null}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 165, "column": 25}, "end": {"line": 165, "column": 28}}, "loc": {"start": {"line": 165, "column": 31}, "end": {"line": 165, "column": 59}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 196, "column": 26}, "end": {"line": 196, "column": 29}}, "loc": {"start": {"line": 196, "column": 31}, "end": {"line": 204, "column": 3}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 199, "column": 32}, "end": {"line": 199, "column": 33}}, "loc": {"start": {"line": 199, "column": 37}, "end": {"line": 199, "column": 59}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 200, "column": 30}, "end": {"line": 200, "column": 31}}, "loc": {"start": {"line": 200, "column": 35}, "end": {"line": 200, "column": 55}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 201, "column": 31}, "end": {"line": 201, "column": 32}}, "loc": {"start": {"line": 201, "column": 36}, "end": {"line": 201, "column": 57}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 240, "column": 50}, "end": {"line": 240, "column": 51}}, "loc": {"start": {"line": 241, "column": 18}, "end": {"line": 242, "column": null}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 244, "column": 29}, "end": {"line": 244, "column": 32}}, "loc": {"start": {"line": 244, "column": 35}, "end": {"line": 244, "column": 61}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 259, "column": 26}, "end": {"line": 259, "column": 27}}, "loc": {"start": {"line": 259, "column": 33}, "end": {"line": 259, "column": 68}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 280, "column": 25}, "end": {"line": 280, "column": 28}}, "loc": {"start": {"line": 280, "column": 30}, "end": {"line": 283, "column": 17}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 291, "column": 34}, "end": {"line": 291, "column": 45}}, "loc": {"start": {"line": 291, "column": 48}, "end": {"line": 318, "column": 17}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 295, "column": 47}, "end": {"line": 295, "column": 53}}, "loc": {"start": {"line": 295, "column": 56}, "end": {"line": 315, "column": 23}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 321, "column": 26}, "end": {"line": 321, "column": 29}}, "loc": {"start": {"line": 321, "column": 32}, "end": {"line": 341, "column": 17}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 329, "column": 31}, "end": {"line": 329, "column": 34}}, "loc": {"start": {"line": 329, "column": 37}, "end": {"line": 329, "column": 65}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 331, "column": 37}, "end": {"line": 331, "column": 41}}, "loc": {"start": {"line": 331, "column": 44}, "end": {"line": 338, "column": 23}}}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 56, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 36}}, {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 34}}, {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 35}}, {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 27}}]}, "1": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 65, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 41}}, {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 39}}, {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 40}}, {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 32}}]}, "2": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}]}, "3": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}]}, "4": {"loc": {"start": {"line": 79, "column": 19}, "end": {"line": 84, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 19}, "end": {"line": 84, "column": 18}}, {"start": {"line": 84, "column": 22}, "end": {"line": 84, "column": 23}}]}, "5": {"loc": {"start": {"line": 148, "column": 13}, "end": {"line": 148, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 148, "column": 21}, "end": {"line": 148, "column": 42}}, {"start": {"line": 148, "column": 45}, "end": {"line": 148, "column": 48}}]}, "6": {"loc": {"start": {"line": 220, "column": 13}, "end": {"line": 232, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 220, "column": 13}, "end": {"line": 220, "column": 21}}, {"start": {"line": 221, "column": 14}, "end": {"line": 231, "column": null}}]}, "7": {"loc": {"start": {"line": 243, "column": 57}, "end": {"line": 243, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 243, "column": 79}, "end": {"line": 243, "column": 92}}, {"start": {"line": 243, "column": 95}, "end": {"line": 243, "column": 97}}]}, "8": {"loc": {"start": {"line": 247, "column": 21}, "end": {"line": 247, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 247, "column": 38}, "end": {"line": 247, "column": 42}}, {"start": {"line": 247, "column": 45}, "end": {"line": 247, "column": 62}}]}, "9": {"loc": {"start": {"line": 273, "column": 11}, "end": {"line": 343, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 12}, "end": {"line": 286, "column": null}}, {"start": {"line": 289, "column": 12}, "end": {"line": 342, "column": null}}]}, "10": {"loc": {"start": {"line": 303, "column": 32}, "end": {"line": 310, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 303, "column": 32}, "end": {"line": 303, "column": 54}}, {"start": {"line": 304, "column": 32}, "end": {"line": 309, "column": null}}]}, "11": {"loc": {"start": {"line": 305, "column": 36}, "end": {"line": 309, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 306, "column": 39}, "end": {"line": 308, "column": 45}}, {"start": {"line": 309, "column": 38}, "end": {"line": 309, "column": 43}}]}, "12": {"loc": {"start": {"line": 306, "column": 39}, "end": {"line": 308, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 307, "column": 40}, "end": {"line": 307, "column": 45}}, {"start": {"line": 308, "column": 40}, "end": {"line": 308, "column": 45}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 21, "7": 21, "8": 21, "9": 1074, "10": 21, "11": 1074, "12": 21, "13": 1137, "14": 558, "15": 540, "16": 39, "17": 0, "18": 21, "19": 1074, "20": 537, "21": 519, "22": 18, "23": 0, "24": 21, "25": 19, "26": 19, "27": 0, "28": 0, "29": 19, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 19, "36": 21, "37": 19, "38": 20, "39": 1074, "40": 1074, "41": 1074, "42": 20, "43": 1074, "44": 1074, "45": 0, "46": 21, "47": 21, "48": 21, "49": 1074, "50": 1074, "51": 1074, "52": 21, "53": 21, "54": 21, "55": 84, "56": 0, "57": 0, "58": 0, "59": 0, "60": 20, "61": 20, "62": 100, "63": 100, "64": 1074, "65": 1074, "66": 1074, "67": 0, "68": 5370, "69": 5370, "70": 1}, "f": {"0": 21, "1": 1074, "2": 1074, "3": 1137, "4": 1074, "5": 19, "6": 0, "7": 0, "8": 19, "9": 20, "10": 1074, "11": 1074, "12": 1074, "13": 20, "14": 1074, "15": 1074, "16": 0, "17": 21, "18": 1074, "19": 1074, "20": 1074, "21": 84, "22": 0, "23": 0, "24": 0, "25": 20, "26": 100, "27": 1074, "28": 0, "29": 5370}, "b": {"0": [558, 540, 39, 0], "1": [537, 519, 18, 0], "2": [0], "3": [0], "4": [0, 0], "5": [1074, 0], "6": [21, 0], "7": [21, 63], "8": [21, 63], "9": [1, 20], "10": [100, 80], "11": [20, 60], "12": [18, 2]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoryPage.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoryPage.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 46}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 57}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 46}}, "5": {"start": {"line": 41, "column": 30}, "end": {"line": 486, "column": 1}}, "6": {"start": {"line": 42, "column": 42}, "end": {"line": 42, "column": 80}}, "7": {"start": {"line": 43, "column": 44}, "end": {"line": 43, "column": 82}}, "8": {"start": {"line": 44, "column": 32}, "end": {"line": 48, "column": 4}}, "9": {"start": {"line": 49, "column": 36}, "end": {"line": 49, "column": 50}}, "10": {"start": {"line": 50, "column": 42}, "end": {"line": 50, "column": 87}}, "11": {"start": {"line": 51, "column": 56}, "end": {"line": 51, "column": 71}}, "12": {"start": {"line": 54, "column": 2}, "end": {"line": 56, "column": 9}}, "13": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 25}}, "14": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 30}}, "15": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 19}}, "16": {"start": {"line": 63, "column": 29}, "end": {"line": 163, "column": 3}}, "17": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 23}}, "18": {"start": {"line": 65, "column": 4}, "end": {"line": 162, "column": 5}}, "19": {"start": {"line": 66, "column": 56}, "end": {"line": 66, "column": 58}}, "20": {"start": {"line": 69, "column": 33}, "end": {"line": 69, "column": 75}}, "21": {"start": {"line": 70, "column": 6}, "end": {"line": 86, "column": 7}}, "22": {"start": {"line": 71, "column": 29}, "end": {"line": 71, "column": 59}}, "23": {"start": {"line": 72, "column": 8}, "end": {"line": 85, "column": 11}}, "24": {"start": {"line": 73, "column": 10}, "end": {"line": 84, "column": 13}}, "25": {"start": {"line": 89, "column": 28}, "end": {"line": 89, "column": 77}}, "26": {"start": {"line": 90, "column": 6}, "end": {"line": 108, "column": 9}}, "27": {"start": {"line": 91, "column": 8}, "end": {"line": 107, "column": 11}}, "28": {"start": {"line": 111, "column": 32}, "end": {"line": 111, "column": 70}}, "29": {"start": {"line": 112, "column": 6}, "end": {"line": 130, "column": 9}}, "30": {"start": {"line": 113, "column": 8}, "end": {"line": 129, "column": 11}}, "31": {"start": {"line": 133, "column": 6}, "end": {"line": 152, "column": 9}}, "32": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 84}}, "33": {"start": {"line": 155, "column": 37}, "end": {"line": 155, "column": 82}}, "34": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 39}}, "35": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 62}}, "36": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": 26}}, "37": {"start": {"line": 165, "column": 23}, "end": {"line": 194, "column": 3}}, "38": {"start": {"line": 166, "column": 19}, "end": {"line": 166, "column": 36}}, "39": {"start": {"line": 169, "column": 16}, "end": {"line": 169, "column": 26}}, "40": {"start": {"line": 170, "column": 24}, "end": {"line": 176, "column": 6}}, "41": {"start": {"line": 178, "column": 4}, "end": {"line": 181, "column": 5}}, "42": {"start": {"line": 179, "column": 25}, "end": {"line": 179, "column": 55}}, "43": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 71}}, "44": {"start": {"line": 180, "column": 41}, "end": {"line": 180, "column": 69}}, "45": {"start": {"line": 184, "column": 4}, "end": {"line": 186, "column": 5}}, "46": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 69}}, "47": {"start": {"line": 185, "column": 41}, "end": {"line": 185, "column": 67}}, "48": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "49": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 82}}, "50": {"start": {"line": 190, "column": 41}, "end": {"line": 190, "column": 80}}, "51": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 31}}, "52": {"start": {"line": 196, "column": 23}, "end": {"line": 199, "column": 3}}, "53": {"start": {"line": 197, "column": 4}, "end": {"line": 197, "column": 50}}, "54": {"start": {"line": 198, "column": 4}, "end": {"line": 198, "column": 88}}, "55": {"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 86}}, "56": {"start": {"line": 198, "column": 48}, "end": {"line": 198, "column": 85}}, "57": {"start": {"line": 201, "column": 24}, "end": {"line": 217, "column": 3}}, "58": {"start": {"line": 202, "column": 23}, "end": {"line": 206, "column": 6}}, "59": {"start": {"line": 208, "column": 17}, "end": {"line": 208, "column": 94}}, "60": {"start": {"line": 209, "column": 16}, "end": {"line": 209, "column": 41}}, "61": {"start": {"line": 210, "column": 14}, "end": {"line": 210, "column": 41}}, "62": {"start": {"line": 211, "column": 4}, "end": {"line": 211, "column": 17}}, "63": {"start": {"line": 212, "column": 4}, "end": {"line": 212, "column": 89}}, "64": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 33}}, "65": {"start": {"line": 214, "column": 4}, "end": {"line": 214, "column": 14}}, "66": {"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": 33}}, "67": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 29}}, "68": {"start": {"line": 219, "column": 22}, "end": {"line": 227, "column": 3}}, "69": {"start": {"line": 220, "column": 18}, "end": {"line": 225, "column": 6}}, "70": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 53}}, "71": {"start": {"line": 229, "column": 25}, "end": {"line": 239, "column": 3}}, "72": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 43}}, "73": {"start": {"line": 230, "column": 21}, "end": {"line": 230, "column": 43}}, "74": {"start": {"line": 232, "column": 19}, "end": {"line": 237, "column": 6}}, "75": {"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 59}}, "76": {"start": {"line": 241, "column": 29}, "end": {"line": 252, "column": 3}}, "77": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 28}}, "78": {"start": {"line": 242, "column": 16}, "end": {"line": 242, "column": 28}}, "79": {"start": {"line": 244, "column": 19}, "end": {"line": 248, "column": 6}}, "80": {"start": {"line": 250, "column": 18}, "end": {"line": 250, "column": 54}}, "81": {"start": {"line": 251, "column": 4}, "end": {"line": 251, "column": 76}}, "82": {"start": {"line": 254, "column": 26}, "end": {"line": 257, "column": 3}}, "83": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 26}}, "84": {"start": {"line": 256, "column": 4}, "end": {"line": 256, "column": 33}}, "85": {"start": {"line": 259, "column": 2}, "end": {"line": 261, "column": 3}}, "86": {"start": {"line": 260, "column": 4}, "end": {"line": 260, "column": 74}}, "87": {"start": {"line": 263, "column": 2}, "end": {"line": 485, "column": 4}}, "88": {"start": {"line": 286, "column": 31}, "end": {"line": 286, "column": 98}}, "89": {"start": {"line": 286, "column": 51}, "end": {"line": 286, "column": 96}}, "90": {"start": {"line": 302, "column": 31}, "end": {"line": 302, "column": 93}}, "91": {"start": {"line": 302, "column": 51}, "end": {"line": 302, "column": 91}}, "92": {"start": {"line": 318, "column": 31}, "end": {"line": 318, "column": 95}}, "93": {"start": {"line": 318, "column": 51}, "end": {"line": 318, "column": 93}}, "94": {"start": {"line": 359, "column": 42}, "end": {"line": 359, "column": 69}}, "95": {"start": {"line": 365, "column": 42}, "end": {"line": 365, "column": 67}}, "96": {"start": {"line": 371, "column": 42}, "end": {"line": 371, "column": 68}}, "97": {"start": {"line": 386, "column": 12}, "end": {"line": 387, "column": null}}, "98": {"start": {"line": 389, "column": 29}, "end": {"line": 389, "column": 50}}, "99": {"start": {"line": 446, "column": 60}, "end": {"line": 446, "column": 89}}, "100": {"start": {"line": 447, "column": 63}, "end": {"line": 447, "column": 82}}, "101": {"start": {"line": 452, "column": 31}, "end": {"line": 452, "column": 60}}, "102": {"start": {"line": 488, "column": 0}, "end": {"line": 488, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 41, "column": 30}, "end": {"line": 41, "column": 33}}, "loc": {"start": {"line": 41, "column": 35}, "end": {"line": 486, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 15}}, "loc": {"start": {"line": 54, "column": 17}, "end": {"line": 56, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 59, "column": 12}, "end": {"line": 59, "column": 15}}, "loc": {"start": {"line": 59, "column": 17}, "end": {"line": 61, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 34}}, "loc": {"start": {"line": 63, "column": 40}, "end": {"line": 163, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 29}, "end": {"line": 72, "column": 30}}, "loc": {"start": {"line": 72, "column": 43}, "end": {"line": 85, "column": 9}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 90, "column": 28}, "end": {"line": 90, "column": 29}}, "loc": {"start": {"line": 90, "column": 49}, "end": {"line": 108, "column": 7}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 112, "column": 32}, "end": {"line": 112, "column": 33}}, "loc": {"start": {"line": 112, "column": 51}, "end": {"line": 130, "column": 7}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 155, "column": 27}, "end": {"line": 155, "column": 28}}, "loc": {"start": {"line": 155, "column": 37}, "end": {"line": 155, "column": 82}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 165, "column": 23}, "end": {"line": 165, "column": 26}}, "loc": {"start": {"line": 165, "column": 28}, "end": {"line": 194, "column": 3}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 180, "column": 33}, "end": {"line": 180, "column": 37}}, "loc": {"start": {"line": 180, "column": 41}, "end": {"line": 180, "column": 69}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 185, "column": 33}, "end": {"line": 185, "column": 37}}, "loc": {"start": {"line": 185, "column": 41}, "end": {"line": 185, "column": 67}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 190, "column": 33}, "end": {"line": 190, "column": 37}}, "loc": {"start": {"line": 190, "column": 41}, "end": {"line": 190, "column": 80}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 196, "column": 23}, "end": {"line": 196, "column": 26}}, "loc": {"start": {"line": 196, "column": 28}, "end": {"line": 199, "column": 3}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 198, "column": 20}, "end": {"line": 198, "column": 24}}, "loc": {"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 86}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 198, "column": 40}, "end": {"line": 198, "column": 44}}, "loc": {"start": {"line": 198, "column": 48}, "end": {"line": 198, "column": 85}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 201, "column": 24}, "end": {"line": 201, "column": 27}}, "loc": {"start": {"line": 201, "column": 29}, "end": {"line": 217, "column": 3}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 219, "column": 22}, "end": {"line": 219, "column": 23}}, "loc": {"start": {"line": 219, "column": 39}, "end": {"line": 227, "column": 3}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 229, "column": 25}, "end": {"line": 229, "column": 26}}, "loc": {"start": {"line": 229, "column": 66}, "end": {"line": 239, "column": 3}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 241, "column": 29}, "end": {"line": 241, "column": 30}}, "loc": {"start": {"line": 241, "column": 48}, "end": {"line": 252, "column": 3}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 254, "column": 26}, "end": {"line": 254, "column": 27}}, "loc": {"start": {"line": 254, "column": 59}, "end": {"line": 257, "column": 3}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 286, "column": 24}, "end": {"line": 286, "column": 25}}, "loc": {"start": {"line": 286, "column": 31}, "end": {"line": 286, "column": 98}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 286, "column": 42}, "end": {"line": 286, "column": 46}}, "loc": {"start": {"line": 286, "column": 51}, "end": {"line": 286, "column": 96}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 302, "column": 24}, "end": {"line": 302, "column": 25}}, "loc": {"start": {"line": 302, "column": 31}, "end": {"line": 302, "column": 93}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 302, "column": 42}, "end": {"line": 302, "column": 46}}, "loc": {"start": {"line": 302, "column": 51}, "end": {"line": 302, "column": 91}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 318, "column": 24}, "end": {"line": 318, "column": 25}}, "loc": {"start": {"line": 318, "column": 31}, "end": {"line": 318, "column": 95}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 318, "column": 42}, "end": {"line": 318, "column": 46}}, "loc": {"start": {"line": 318, "column": 51}, "end": {"line": 318, "column": 93}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 359, "column": 34}, "end": {"line": 359, "column": 38}}, "loc": {"start": {"line": 359, "column": 42}, "end": {"line": 359, "column": 69}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 365, "column": 34}, "end": {"line": 365, "column": 38}}, "loc": {"start": {"line": 365, "column": 42}, "end": {"line": 365, "column": 67}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 371, "column": 34}, "end": {"line": 371, "column": 38}}, "loc": {"start": {"line": 371, "column": 42}, "end": {"line": 371, "column": 68}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 385, "column": 28}, "end": {"line": 385, "column": 29}}, "loc": {"start": {"line": 386, "column": 12}, "end": {"line": 387, "column": null}}}, "30": {"name": "(anonymous_31)", "decl": {"start": {"line": 389, "column": 23}, "end": {"line": 389, "column": 26}}, "loc": {"start": {"line": 389, "column": 29}, "end": {"line": 389, "column": 50}}}, "31": {"name": "(anonymous_32)", "decl": {"start": {"line": 446, "column": 54}, "end": {"line": 446, "column": 57}}, "loc": {"start": {"line": 446, "column": 60}, "end": {"line": 446, "column": 89}}}, "32": {"name": "(anonymous_33)", "decl": {"start": {"line": 447, "column": 56}, "end": {"line": 447, "column": 57}}, "loc": {"start": {"line": 447, "column": 63}, "end": {"line": 447, "column": 82}}}, "33": {"name": "(anonymous_34)", "decl": {"start": {"line": 452, "column": 25}, "end": {"line": 452, "column": 28}}, "loc": {"start": {"line": 452, "column": 31}, "end": {"line": 452, "column": 60}}}}, "branchMap": {"0": {"loc": {"start": {"line": 70, "column": 6}, "end": {"line": 86, "column": 7}}, "type": "if", "locations": [{"start": {"line": 70, "column": 6}, "end": {"line": 86, "column": 7}}]}, "1": {"loc": {"start": {"line": 178, "column": 4}, "end": {"line": 181, "column": 5}}, "type": "if", "locations": [{"start": {"line": 178, "column": 4}, "end": {"line": 181, "column": 5}}]}, "2": {"loc": {"start": {"line": 184, "column": 4}, "end": {"line": 186, "column": 5}}, "type": "if", "locations": [{"start": {"line": 184, "column": 4}, "end": {"line": 186, "column": 5}}]}, "3": {"loc": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "type": "if", "locations": [{"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}]}, "4": {"loc": {"start": {"line": 226, "column": 11}, "end": {"line": 226, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 226, "column": 11}, "end": {"line": 226, "column": 44}}, {"start": {"line": 226, "column": 48}, "end": {"line": 226, "column": 52}}]}, "5": {"loc": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 43}}, "type": "if", "locations": [{"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 43}}]}, "6": {"loc": {"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 48}}, {"start": {"line": 238, "column": 52}, "end": {"line": 238, "column": 58}}]}, "7": {"loc": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 28}}, "type": "if", "locations": [{"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 28}}]}, "8": {"loc": {"start": {"line": 251, "column": 11}, "end": {"line": 251, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 251, "column": 19}, "end": {"line": 251, "column": 68}}, {"start": {"line": 251, "column": 71}, "end": {"line": 251, "column": 75}}]}, "9": {"loc": {"start": {"line": 259, "column": 2}, "end": {"line": 261, "column": 3}}, "type": "if", "locations": [{"start": {"line": 259, "column": 2}, "end": {"line": 261, "column": 3}}]}, "10": {"loc": {"start": {"line": 378, "column": 9}, "end": {"line": 440, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 379, "column": 10}, "end": {"line": 382, "column": null}}, {"start": {"line": 385, "column": 10}, "end": {"line": 440, "column": 12}}]}, "11": {"loc": {"start": {"line": 403, "column": 60}, "end": {"line": 403, "column": 94}}, "type": "binary-expr", "locations": [{"start": {"line": 403, "column": 60}, "end": {"line": 403, "column": 85}}, {"start": {"line": 403, "column": 89}, "end": {"line": 403, "column": 94}}]}, "12": {"loc": {"start": {"line": 411, "column": 17}, "end": {"line": 415, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 411, "column": 17}, "end": {"line": 411, "column": 44}}, {"start": {"line": 412, "column": 18}, "end": {"line": 414, "column": null}}]}, "13": {"loc": {"start": {"line": 418, "column": 17}, "end": {"line": 422, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 418, "column": 17}, "end": {"line": 418, "column": 49}}, {"start": {"line": 419, "column": 18}, "end": {"line": 421, "column": null}}]}, "14": {"loc": {"start": {"line": 425, "column": 17}, "end": {"line": 429, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 425, "column": 17}, "end": {"line": 425, "column": 46}}, {"start": {"line": 426, "column": 18}, "end": {"line": 428, "column": null}}]}, "15": {"loc": {"start": {"line": 432, "column": 17}, "end": {"line": 436, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 432, "column": 17}, "end": {"line": 432, "column": 41}}, {"start": {"line": 433, "column": 18}, "end": {"line": 435, "column": null}}]}, "16": {"loc": {"start": {"line": 445, "column": 7}, "end": {"line": 482, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 445, "column": 7}, "end": {"line": 445, "column": 26}}, {"start": {"line": 445, "column": 30}, "end": {"line": 445, "column": 42}}, {"start": {"line": 446, "column": 8}, "end": {"line": 481, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 71, "7": 71, "8": 71, "9": 71, "10": 71, "11": 71, "12": 71, "13": 18, "14": 71, "15": 42, "16": 71, "17": 18, "18": 18, "19": 18, "20": 18, "21": 18, "22": 2, "23": 2, "24": 2, "25": 18, "26": 18, "27": 36, "28": 18, "29": 18, "30": 36, "31": 18, "32": 18, "33": 164, "34": 18, "35": 0, "36": 18, "37": 71, "38": 42, "39": 42, "40": 42, "41": 42, "42": 2, "43": 2, "44": 10, "45": 42, "46": 3, "47": 10, "48": 42, "49": 1, "50": 5, "51": 42, "52": 71, "53": 1, "54": 1, "55": 1, "56": 6, "57": 71, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 71, "69": 152, "70": 152, "71": 71, "72": 149, "73": 146, "74": 3, "75": 3, "76": 71, "77": 149, "78": 3, "79": 146, "80": 146, "81": 146, "82": 71, "83": 3, "84": 3, "85": 71, "86": 18, "87": 53, "88": 2, "89": 2, "90": 2, "91": 2, "92": 1, "93": 1, "94": 149, "95": 149, "96": 149, "97": 149, "98": 3, "99": 1, "100": 1, "101": 1, "102": 1}, "f": {"0": 71, "1": 18, "2": 42, "3": 18, "4": 2, "5": 36, "6": 36, "7": 164, "8": 42, "9": 10, "10": 10, "11": 5, "12": 1, "13": 1, "14": 6, "15": 1, "16": 152, "17": 149, "18": 149, "19": 3, "20": 2, "21": 2, "22": 2, "23": 2, "24": 1, "25": 1, "26": 149, "27": 149, "28": 149, "29": 149, "30": 3, "31": 1, "32": 1, "33": 1}, "b": {"0": [2], "1": [2], "2": [3], "3": [1], "4": [152, 0], "5": [146], "6": [3, 0], "7": [3], "8": [146, 0], "9": [18], "10": [23, 30], "11": [149, 3], "12": [149, 3], "13": [149, 58], "14": [149, 58], "15": [149, 30], "16": [53, 3, 3]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/LoadingSpinner.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/LoadingSpinner.tsx", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "1": {"start": {"line": 9, "column": 54}, "end": {"line": 28, "column": 1}}, "2": {"start": {"line": 13, "column": 2}, "end": {"line": 27, "column": 4}}, "3": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 54}, "end": {"line": 9, "column": 55}}, "loc": {"start": {"line": 12, "column": 5}, "end": {"line": 28, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 24}}]}, "1": {"loc": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 17}}]}, "2": {"loc": {"start": {"line": 24, "column": 9}, "end": {"line": 24, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 9}, "end": {"line": 24, "column": 25}}, {"start": {"line": 24, "column": 29}, "end": {"line": 24, "column": 79}}]}}, "s": {"0": 3, "1": 3, "2": 23, "3": 3}, "f": {"0": 23}, "b": {"0": [4], "1": [22], "2": [23, 22]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/Navigation.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/Navigation.tsx", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "1": {"start": {"line": 17, "column": 46}, "end": {"line": 46, "column": 1}}, "2": {"start": {"line": 18, "column": 16}, "end": {"line": 25, "column": 4}}, "3": {"start": {"line": 27, "column": 2}, "end": {"line": 45, "column": 4}}, "4": {"start": {"line": 31, "column": 10}, "end": {"line": 32, "column": null}}, "5": {"start": {"line": 34, "column": 27}, "end": {"line": 34, "column": 43}}, "6": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 46}, "end": {"line": 17, "column": 47}}, "loc": {"start": {"line": 17, "column": 79}, "end": {"line": 46, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 20}}, "loc": {"start": {"line": 31, "column": 10}, "end": {"line": 32, "column": null}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 24}}, "loc": {"start": {"line": 34, "column": 27}, "end": {"line": 34, "column": 43}}}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 46}, "end": {"line": 33, "column": 84}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 66}, "end": {"line": 33, "column": 79}}, {"start": {"line": 33, "column": 82}, "end": {"line": 33, "column": 84}}]}, "1": {"loc": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 35, "column": 46}, "end": {"line": 35, "column": 52}}, {"start": {"line": 35, "column": 55}, "end": {"line": 35, "column": 64}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/ProjectionsChart.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/ProjectionsChart.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 40}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 53}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 51}}, "5": {"start": {"line": 13, "column": 58}, "end": {"line": 288, "column": 1}}, "6": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 46}}, "7": {"start": {"line": 18, "column": 46}, "end": {"line": 18, "column": 97}}, "8": {"start": {"line": 19, "column": 40}, "end": {"line": 19, "column": 82}}, "9": {"start": {"line": 21, "column": 25}, "end": {"line": 28, "column": 3}}, "10": {"start": {"line": 22, "column": 4}, "end": {"line": 27, "column": 22}}, "11": {"start": {"line": 30, "column": 2}, "end": {"line": 189, "column": 36}}, "12": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 57}}, "13": {"start": {"line": 31, "column": 50}, "end": {"line": 31, "column": 57}}, "14": {"start": {"line": 33, "column": 16}, "end": {"line": 33, "column": 43}}, "15": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 32}}, "16": {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 63}}, "17": {"start": {"line": 37, "column": 18}, "end": {"line": 37, "column": 50}}, "18": {"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 51}}, "19": {"start": {"line": 40, "column": 14}, "end": {"line": 44, "column": 67}}, "20": {"start": {"line": 47, "column": 19}, "end": {"line": 50, "column": 24}}, "21": {"start": {"line": 49, "column": 42}, "end": {"line": 49, "column": 48}}, "22": {"start": {"line": 52, "column": 19}, "end": {"line": 65, "column": 25}}, "23": {"start": {"line": 57, "column": 10}, "end": {"line": 62, "column": 11}}, "24": {"start": {"line": 58, "column": 27}, "end": {"line": 58, "column": 52}}, "25": {"start": {"line": 59, "column": 27}, "end": {"line": 59, "column": 52}}, "26": {"start": {"line": 60, "column": 28}, "end": {"line": 60, "column": 54}}, "27": {"start": {"line": 61, "column": 21}, "end": {"line": 61, "column": 46}}, "28": {"start": {"line": 68, "column": 17}, "end": {"line": 79, "column": 31}}, "29": {"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 28}}, "30": {"start": {"line": 72, "column": 8}, "end": {"line": 77, "column": 9}}, "31": {"start": {"line": 73, "column": 25}, "end": {"line": 73, "column": 58}}, "32": {"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 58}}, "33": {"start": {"line": 75, "column": 26}, "end": {"line": 75, "column": 60}}, "34": {"start": {"line": 76, "column": 19}, "end": {"line": 76, "column": 52}}, "35": {"start": {"line": 82, "column": 21}, "end": {"line": 88, "column": 34}}, "36": {"start": {"line": 90, "column": 4}, "end": {"line": 93, "column": 54}}, "37": {"start": {"line": 95, "column": 4}, "end": {"line": 98, "column": 54}}, "38": {"start": {"line": 101, "column": 17}, "end": {"line": 113, "column": 31}}, "39": {"start": {"line": 103, "column": 14}, "end": {"line": 103, "column": 28}}, "40": {"start": {"line": 106, "column": 8}, "end": {"line": 111, "column": 9}}, "41": {"start": {"line": 107, "column": 25}, "end": {"line": 107, "column": 58}}, "42": {"start": {"line": 108, "column": 25}, "end": {"line": 108, "column": 58}}, "43": {"start": {"line": 109, "column": 26}, "end": {"line": 109, "column": 60}}, "44": {"start": {"line": 110, "column": 19}, "end": {"line": 110, "column": 52}}, "45": {"start": {"line": 115, "column": 4}, "end": {"line": 118, "column": 23}}, "46": {"start": {"line": 121, "column": 4}, "end": {"line": 127, "column": 65}}, "47": {"start": {"line": 130, "column": 4}, "end": {"line": 168, "column": 9}}, "48": {"start": {"line": 134, "column": 23}, "end": {"line": 134, "column": 37}}, "49": {"start": {"line": 136, "column": 8}, "end": {"line": 141, "column": 9}}, "50": {"start": {"line": 137, "column": 25}, "end": {"line": 137, "column": 58}}, "51": {"start": {"line": 138, "column": 25}, "end": {"line": 138, "column": 58}}, "52": {"start": {"line": 139, "column": 26}, "end": {"line": 139, "column": 60}}, "53": {"start": {"line": 140, "column": 19}, "end": {"line": 140, "column": 52}}, "54": {"start": {"line": 149, "column": 8}, "end": {"line": 153, "column": 35}}, "55": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 26}}, "56": {"start": {"line": 157, "column": 8}, "end": {"line": 161, "column": 35}}, "57": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 29}}, "58": {"start": {"line": 165, "column": 8}, "end": {"line": 167, "column": 9}}, "59": {"start": {"line": 166, "column": 10}, "end": {"line": 166, "column": 26}}, "60": {"start": {"line": 171, "column": 4}, "end": {"line": 175, "column": 49}}, "61": {"start": {"line": 173, "column": 50}, "end": {"line": 173, "column": 61}}, "62": {"start": {"line": 177, "column": 4}, "end": {"line": 180, "column": 49}}, "63": {"start": {"line": 178, "column": 48}, "end": {"line": 178, "column": 75}}, "64": {"start": {"line": 183, "column": 4}, "end": {"line": 184, "column": 51}}, "65": {"start": {"line": 186, "column": 4}, "end": {"line": 187, "column": 51}}, "66": {"start": {"line": 191, "column": 25}, "end": {"line": 198, "column": 3}}, "67": {"start": {"line": 192, "column": 4}, "end": {"line": 197, "column": 5}}, "68": {"start": {"line": 193, "column": 21}, "end": {"line": 193, "column": 55}}, "69": {"start": {"line": 194, "column": 21}, "end": {"line": 194, "column": 55}}, "70": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 57}}, "71": {"start": {"line": 196, "column": 15}, "end": {"line": 196, "column": 49}}, "72": {"start": {"line": 200, "column": 25}, "end": {"line": 207, "column": 3}}, "73": {"start": {"line": 201, "column": 4}, "end": {"line": 206, "column": 5}}, "74": {"start": {"line": 202, "column": 21}, "end": {"line": 202, "column": 47}}, "75": {"start": {"line": 203, "column": 21}, "end": {"line": 203, "column": 47}}, "76": {"start": {"line": 204, "column": 22}, "end": {"line": 204, "column": 49}}, "77": {"start": {"line": 205, "column": 15}, "end": {"line": 205, "column": 41}}, "78": {"start": {"line": 209, "column": 2}, "end": {"line": 287, "column": 4}}, "79": {"start": {"line": 222, "column": 14}, "end": {"line": 223, "column": null}}, "80": {"start": {"line": 229, "column": 33}, "end": {"line": 229, "column": 93}}, "81": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 32}}}, "fnMap": {"0": {"name": "(anonymous_10)", "decl": {"start": {"line": 13, "column": 58}, "end": {"line": 13, "column": 59}}, "loc": {"start": {"line": 16, "column": 5}, "end": {"line": 288, "column": 1}}}, "1": {"name": "(anonymous_11)", "decl": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 26}}, "loc": {"start": {"line": 21, "column": 44}, "end": {"line": 28, "column": 3}}}, "2": {"name": "(anonymous_12)", "decl": {"start": {"line": 30, "column": 12}, "end": {"line": 30, "column": 15}}, "loc": {"start": {"line": 30, "column": 17}, "end": {"line": 189, "column": 3}}}, "3": {"name": "(anonymous_13)", "decl": {"start": {"line": 49, "column": 37}, "end": {"line": 49, "column": 38}}, "loc": {"start": {"line": 49, "column": 42}, "end": {"line": 49, "column": 48}}}, "4": {"name": "(anonymous_14)", "decl": {"start": {"line": 56, "column": 28}, "end": {"line": 56, "column": 29}}, "loc": {"start": {"line": 56, "column": 32}, "end": {"line": 63, "column": 9}}}, "5": {"name": "(anonymous_15)", "decl": {"start": {"line": 70, "column": 9}, "end": {"line": 70, "column": 10}}, "loc": {"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 28}}}, "6": {"name": "(anonymous_16)", "decl": {"start": {"line": 71, "column": 9}, "end": {"line": 71, "column": 10}}, "loc": {"start": {"line": 71, "column": 13}, "end": {"line": 78, "column": 7}}}, "7": {"name": "(anonymous_17)", "decl": {"start": {"line": 103, "column": 9}, "end": {"line": 103, "column": 10}}, "loc": {"start": {"line": 103, "column": 14}, "end": {"line": 103, "column": 28}}}, "8": {"name": "(anonymous_18)", "decl": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 11}}, "loc": {"start": {"line": 105, "column": 14}, "end": {"line": 112, "column": 7}}}, "9": {"name": "(anonymous_19)", "decl": {"start": {"line": 134, "column": 18}, "end": {"line": 134, "column": 19}}, "loc": {"start": {"line": 134, "column": 23}, "end": {"line": 134, "column": 37}}}, "10": {"name": "(anonymous_20)", "decl": {"start": {"line": 135, "column": 18}, "end": {"line": 135, "column": 19}}, "loc": {"start": {"line": 135, "column": 22}, "end": {"line": 142, "column": 7}}}, "11": {"name": "(anonymous_21)", "decl": {"start": {"line": 148, "column": 23}, "end": {"line": 148, "column": 32}}, "loc": {"start": {"line": 148, "column": 40}, "end": {"line": 155, "column": 7}}}, "12": {"name": "(anonymous_22)", "decl": {"start": {"line": 156, "column": 22}, "end": {"line": 156, "column": null}}, "loc": {"start": {"line": 156, "column": 22}, "end": {"line": 163, "column": 7}}}, "13": {"name": "(anonymous_23)", "decl": {"start": {"line": 164, "column": 19}, "end": {"line": 164, "column": 28}}, "loc": {"start": {"line": 164, "column": 36}, "end": {"line": 168, "column": 7}}}, "14": {"name": "(anonymous_24)", "decl": {"start": {"line": 173, "column": 45}, "end": {"line": 173, "column": 46}}, "loc": {"start": {"line": 173, "column": 50}, "end": {"line": 173, "column": 61}}}, "15": {"name": "(anonymous_25)", "decl": {"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 44}}, "loc": {"start": {"line": 178, "column": 48}, "end": {"line": 178, "column": 75}}}, "16": {"name": "(anonymous_26)", "decl": {"start": {"line": 191, "column": 25}, "end": {"line": 191, "column": 26}}, "loc": {"start": {"line": 191, "column": 77}, "end": {"line": 198, "column": 3}}}, "17": {"name": "(anonymous_27)", "decl": {"start": {"line": 200, "column": 25}, "end": {"line": 200, "column": 26}}, "loc": {"start": {"line": 200, "column": 44}, "end": {"line": 207, "column": 3}}}, "18": {"name": "(anonymous_28)", "decl": {"start": {"line": 221, "column": 49}, "end": {"line": 221, "column": 55}}, "loc": {"start": {"line": 222, "column": 14}, "end": {"line": 223, "column": null}}}, "19": {"name": "(anonymous_29)", "decl": {"start": {"line": 229, "column": 27}, "end": {"line": 229, "column": 30}}, "loc": {"start": {"line": 229, "column": 33}, "end": {"line": 229, "column": 93}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 57}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 57}}]}, "1": {"loc": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 25}}, {"start": {"line": 31, "column": 29}, "end": {"line": 31, "column": 48}}]}, "2": {"loc": {"start": {"line": 57, "column": 10}, "end": {"line": 62, "column": 11}}, "type": "switch", "locations": [{"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 52}}, {"start": {"line": 59, "column": 12}, "end": {"line": 59, "column": 52}}, {"start": {"line": 60, "column": 12}, "end": {"line": 60, "column": 54}}, {"start": {"line": 61, "column": 12}, "end": {"line": 61, "column": 46}}]}, "3": {"loc": {"start": {"line": 72, "column": 8}, "end": {"line": 77, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 73, "column": 10}, "end": {"line": 73, "column": 58}}, {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 58}}, {"start": {"line": 75, "column": 10}, "end": {"line": 75, "column": 60}}, {"start": {"line": 76, "column": 10}, "end": {"line": 76, "column": 52}}]}, "4": {"loc": {"start": {"line": 106, "column": 8}, "end": {"line": 111, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": 58}}, {"start": {"line": 108, "column": 10}, "end": {"line": 108, "column": 58}}, {"start": {"line": 109, "column": 10}, "end": {"line": 109, "column": 60}}, {"start": {"line": 110, "column": 10}, "end": {"line": 110, "column": 52}}]}, "5": {"loc": {"start": {"line": 136, "column": 8}, "end": {"line": 141, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 137, "column": 10}, "end": {"line": 137, "column": 58}}, {"start": {"line": 138, "column": 10}, "end": {"line": 138, "column": 58}}, {"start": {"line": 139, "column": 10}, "end": {"line": 139, "column": 60}}, {"start": {"line": 140, "column": 10}, "end": {"line": 140, "column": 52}}]}, "6": {"loc": {"start": {"line": 165, "column": 8}, "end": {"line": 167, "column": 9}}, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 167, "column": 9}}]}, "7": {"loc": {"start": {"line": 192, "column": 4}, "end": {"line": 197, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 55}}, {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 55}}, {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 57}}, {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 49}}]}, "8": {"loc": {"start": {"line": 201, "column": 4}, "end": {"line": 206, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 202, "column": 6}, "end": {"line": 202, "column": 47}}, {"start": {"line": 203, "column": 6}, "end": {"line": 203, "column": 47}}, {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 49}}, {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 41}}]}, "9": {"loc": {"start": {"line": 228, "column": 55}, "end": {"line": 228, "column": 101}}, "type": "cond-expr", "locations": [{"start": {"line": 228, "column": 83}, "end": {"line": 228, "column": 96}}, {"start": {"line": 228, "column": 99}, "end": {"line": 228, "column": 101}}]}, "10": {"loc": {"start": {"line": 242, "column": 11}, "end": {"line": 267, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 11}, "end": {"line": 242, "column": 22}}, {"start": {"line": 243, "column": 12}, "end": {"line": 266, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 23, "7": 23, "8": 23, "9": 23, "10": 0, "11": 23, "12": 23, "13": 1, "14": 22, "15": 22, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 23, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 23, "73": 69, "74": 23, "75": 23, "76": 23, "77": 0, "78": 23, "79": 69, "80": 0, "81": 1}, "f": {"0": 23, "1": 0, "2": 23, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 69, "18": 69, "19": 0}, "b": {"0": [1], "1": [23, 23], "2": [0, 0, 0, 0], "3": [0, 0, 0, 0], "4": [0, 0, 0, 0], "5": [0, 0, 0, 0], "6": [0], "7": [0, 0, 0, 0], "8": [23, 23, 23, 0], "9": [23, 46], "10": [23, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/TooltipInfo.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/TooltipInfo.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 12, "column": 48}, "end": {"line": 135, "column": 1}}, "3": {"start": {"line": 19, "column": 36}, "end": {"line": 19, "column": 51}}, "4": {"start": {"line": 20, "column": 48}, "end": {"line": 20, "column": 72}}, "5": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 56}}, "6": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 49}}, "7": {"start": {"line": 23, "column": 21}, "end": {"line": 23, "column": 49}}, "8": {"start": {"line": 25, "column": 22}, "end": {"line": 79, "column": 3}}, "9": {"start": {"line": 26, "column": 4}, "end": {"line": 28, "column": 5}}, "10": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 39}}, "11": {"start": {"line": 30, "column": 4}, "end": {"line": 78, "column": 14}}, "12": {"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 62}}, "13": {"start": {"line": 32, "column": 6}, "end": {"line": 77, "column": 7}}, "14": {"start": {"line": 33, "column": 16}, "end": {"line": 33, "column": 29}}, "15": {"start": {"line": 34, "column": 16}, "end": {"line": 34, "column": 29}}, "16": {"start": {"line": 37, "column": 30}, "end": {"line": 37, "column": 47}}, "17": {"start": {"line": 38, "column": 31}, "end": {"line": 38, "column": 49}}, "18": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 32}}, "19": {"start": {"line": 40, "column": 30}, "end": {"line": 40, "column": 33}}, "20": {"start": {"line": 42, "column": 8}, "end": {"line": 59, "column": 9}}, "21": {"start": {"line": 44, "column": 12}, "end": {"line": 44, "column": 43}}, "22": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 30}}, "23": {"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": 18}}, "24": {"start": {"line": 48, "column": 12}, "end": {"line": 48, "column": 43}}, "25": {"start": {"line": 49, "column": 12}, "end": {"line": 49, "column": 33}}, "26": {"start": {"line": 50, "column": 12}, "end": {"line": 50, "column": 18}}, "27": {"start": {"line": 52, "column": 12}, "end": {"line": 52, "column": 31}}, "28": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 43}}, "29": {"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 18}}, "30": {"start": {"line": 56, "column": 12}, "end": {"line": 56, "column": 32}}, "31": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 43}}, "32": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 18}}, "33": {"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 9}}, "34": {"start": {"line": 63, "column": 10}, "end": {"line": 63, "column": 48}}, "35": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}, "36": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 17}}, "37": {"start": {"line": 68, "column": 8}, "end": {"line": 70, "column": 9}}, "38": {"start": {"line": 69, "column": 10}, "end": {"line": 69, "column": 50}}, "39": {"start": {"line": 71, "column": 8}, "end": {"line": 73, "column": 9}}, "40": {"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 17}}, "41": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 37}}, "42": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 27}}, "43": {"start": {"line": 81, "column": 22}, "end": {"line": 87, "column": 3}}, "44": {"start": {"line": 82, "column": 4}, "end": {"line": 85, "column": 5}}, "45": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 39}}, "46": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 32}}, "47": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 24}}, "48": {"start": {"line": 89, "column": 2}, "end": {"line": 95, "column": 9}}, "49": {"start": {"line": 90, "column": 4}, "end": {"line": 94, "column": 6}}, "50": {"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": 7}}, "51": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 41}}, "52": {"start": {"line": 97, "column": 2}, "end": {"line": 134, "column": 4}}, "53": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 48}, "end": {"line": 12, "column": 49}}, "loc": {"start": {"line": 18, "column": 5}, "end": {"line": 135, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 23}}, "loc": {"start": {"line": 25, "column": 50}, "end": {"line": 79, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 30, "column": 36}, "end": {"line": 30, "column": 39}}, "loc": {"start": {"line": 30, "column": 41}, "end": {"line": 78, "column": 5}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 25}}, "loc": {"start": {"line": 81, "column": 27}, "end": {"line": 87, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 89, "column": 12}, "end": {"line": 89, "column": 15}}, "loc": {"start": {"line": 89, "column": 17}, "end": {"line": 95, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 90, "column": 11}, "end": {"line": 90, "column": 14}}, "loc": {"start": {"line": 90, "column": 16}, "end": {"line": 94, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 18}}]}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 10}, "end": {"line": 17, "column": 13}}]}, "2": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 28, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 28, "column": 5}}]}, "3": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 77, "column": 7}}, "type": "if", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 77, "column": 7}}]}, "4": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 59, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 43, "column": 10}, "end": {"line": 46, "column": 18}}, {"start": {"line": 47, "column": 10}, "end": {"line": 50, "column": 18}}, {"start": {"line": 51, "column": 10}, "end": {"line": 54, "column": 18}}, {"start": {"line": 55, "column": 10}, "end": {"line": 58, "column": 18}}]}, "5": {"loc": {"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 9}}, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 9}}]}, "6": {"loc": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}, "type": "if", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}]}, "7": {"loc": {"start": {"line": 68, "column": 8}, "end": {"line": 70, "column": 9}}, "type": "if", "locations": [{"start": {"line": 68, "column": 8}, "end": {"line": 70, "column": 9}}]}, "8": {"loc": {"start": {"line": 71, "column": 8}, "end": {"line": 73, "column": 9}}, "type": "if", "locations": [{"start": {"line": 71, "column": 8}, "end": {"line": 73, "column": 9}}]}, "9": {"loc": {"start": {"line": 82, "column": 4}, "end": {"line": 85, "column": 5}}, "type": "if", "locations": [{"start": {"line": 82, "column": 4}, "end": {"line": 85, "column": 5}}]}, "10": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": 7}}, "type": "if", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": 7}}]}, "11": {"loc": {"start": {"line": 110, "column": 7}, "end": {"line": 131, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 7}, "end": {"line": 110, "column": 16}}, {"start": {"line": 111, "column": 8}, "end": {"line": 130, "column": null}}]}, "12": {"loc": {"start": {"line": 122, "column": 15}, "end": {"line": 123, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 122, "column": 15}, "end": {"line": 122, "column": 20}}, {"start": {"line": 123, "column": 16}, "end": {"line": 123, "column": 66}}]}}, "s": {"0": 4, "1": 4, "2": 4, "3": 446, "4": 446, "5": 446, "6": 446, "7": 446, "8": 446, "9": 13, "10": 2, "11": 13, "12": 9, "13": 9, "14": 9, "15": 9, "16": 9, "17": 9, "18": 9, "19": 9, "20": 9, "21": 9, "22": 9, "23": 9, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 9, "34": 0, "35": 9, "36": 9, "37": 9, "38": 0, "39": 9, "40": 9, "41": 9, "42": 9, "43": 446, "44": 2, "45": 2, "46": 2, "47": 2, "48": 446, "49": 150, "50": 150, "51": 9, "52": 446, "53": 4}, "f": {"0": 446, "1": 13, "2": 9, "3": 2, "4": 150, "5": 150}, "b": {"0": [446], "1": [446], "2": [2], "3": [9], "4": [9, 0, 0, 0], "5": [0], "6": [9], "7": [0], "8": [9], "9": [2], "10": [9], "11": [446, 9], "12": [9, 8]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthAnalysisDisplay.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthAnalysisDisplay.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 56}}, "1": {"start": {"line": 9, "column": 68}, "end": {"line": 150, "column": 1}}, "2": {"start": {"line": 10, "column": 25}, "end": {"line": 17, "column": 3}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 16, "column": 22}}, "4": {"start": {"line": 19, "column": 27}, "end": {"line": 21, "column": 3}}, "5": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 41}}, "6": {"start": {"line": 23, "column": 27}, "end": {"line": 33, "column": 3}}, "7": {"start": {"line": 24, "column": 45}, "end": {"line": 31, "column": 6}}, "8": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 36}}, "9": {"start": {"line": 35, "column": 2}, "end": {"line": 149, "column": 4}}, "10": {"start": {"line": 91, "column": 35}, "end": {"line": 91, "column": 71}}, "11": {"start": {"line": 92, "column": 16}, "end": {"line": 116, "column": 18}}, "12": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 37}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 68}, "end": {"line": 9, "column": 69}}, "loc": {"start": {"line": 9, "column": 85}, "end": {"line": 150, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 26}}, "loc": {"start": {"line": 10, "column": 44}, "end": {"line": 17, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 19, "column": 27}, "end": {"line": 19, "column": 28}}, "loc": {"start": {"line": 19, "column": 44}, "end": {"line": 21, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 23, "column": 27}, "end": {"line": 23, "column": 28}}, "loc": {"start": {"line": 23, "column": 49}, "end": {"line": 33, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 90, "column": 59}, "end": {"line": 90, "column": 60}}, "loc": {"start": {"line": 90, "column": 82}, "end": {"line": 117, "column": 15}}}}, "branchMap": {"0": {"loc": {"start": {"line": 32, "column": 11}, "end": {"line": 32, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 11}, "end": {"line": 32, "column": 27}}, {"start": {"line": 32, "column": 31}, "end": {"line": 32, "column": 35}}]}, "1": {"loc": {"start": {"line": 140, "column": 19}, "end": {"line": 140, "column": 104}}, "type": "cond-expr", "locations": [{"start": {"line": 140, "column": 62}, "end": {"line": 140, "column": 66}}, {"start": {"line": 140, "column": 69}, "end": {"line": 140, "column": 104}}]}, "2": {"loc": {"start": {"line": 140, "column": 69}, "end": {"line": 140, "column": 104}}, "type": "cond-expr", "locations": [{"start": {"line": 140, "column": 93}, "end": {"line": 140, "column": 97}}, {"start": {"line": 140, "column": 100}, "end": {"line": 140, "column": 104}}]}, "3": {"loc": {"start": {"line": 141, "column": 19}, "end": {"line": 141, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 141, "column": 43}, "end": {"line": 141, "column": 53}}, {"start": {"line": 141, "column": 56}, "end": {"line": 141, "column": 66}}]}}, "s": {"0": 1, "1": 1, "2": 12, "3": 108, "4": 12, "5": 12, "6": 12, "7": 48, "8": 48, "9": 12, "10": 48, "11": 48, "12": 1}, "f": {"0": 12, "1": 108, "2": 12, "3": 48, "4": 48}, "b": {"0": [48, 0], "1": [0, 12], "2": [1, 11], "3": [10, 2]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthTrackerForm.tsx": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthTrackerForm.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 61}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 52}}, "4": {"start": {"line": 27, "column": 60}, "end": {"line": 295, "column": 1}}, "5": {"start": {"line": 28, "column": 36}, "end": {"line": 28, "column": 82}}, "6": {"start": {"line": 29, "column": 83}, "end": {"line": 29, "column": 101}}, "7": {"start": {"line": 31, "column": 67}, "end": {"line": 31, "column": 86}}, "8": {"start": {"line": 33, "column": 19}, "end": {"line": 60, "column": 3}}, "9": {"start": {"line": 34, "column": 18}, "end": {"line": 34, "column": 23}}, "10": {"start": {"line": 36, "column": 4}, "end": {"line": 54, "column": 5}}, "11": {"start": {"line": 37, "column": 43}, "end": {"line": 41, "column": 8}}, "12": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 54}}, "13": {"start": {"line": 44, "column": 39}, "end": {"line": 52, "column": 8}}, "14": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 50}}, "15": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}, "16": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 14}}, "17": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 20}}, "18": {"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 56}}, "19": {"start": {"line": 64, "column": 2}, "end": {"line": 294, "column": 4}}, "20": {"start": {"line": 73, "column": 29}, "end": {"line": 73, "column": 54}}, "21": {"start": {"line": 81, "column": 29}, "end": {"line": 81, "column": 52}}, "22": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 33}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 60}, "end": {"line": 27, "column": 61}}, "loc": {"start": {"line": 27, "column": 80}, "end": {"line": 295, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 24}}, "loc": {"start": {"line": 33, "column": 44}, "end": {"line": 60, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 26}}, "loc": {"start": {"line": 73, "column": 29}, "end": {"line": 73, "column": 54}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 23}, "end": {"line": 81, "column": 26}}, "loc": {"start": {"line": 81, "column": 29}, "end": {"line": 81, "column": 52}}}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {"line": 43, "column": 11}, "end": {"line": 54, "column": 5}}]}, "1": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}]}, "2": {"loc": {"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 37}}, {"start": {"line": 62, "column": 41}, "end": {"line": 62, "column": 56}}]}, "3": {"loc": {"start": {"line": 72, "column": 42}, "end": {"line": 72, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 70}, "end": {"line": 72, "column": 83}}, {"start": {"line": 72, "column": 86}, "end": {"line": 72, "column": 88}}]}, "4": {"loc": {"start": {"line": 80, "column": 42}, "end": {"line": 80, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 80, "column": 68}, "end": {"line": 80, "column": 81}}, {"start": {"line": 80, "column": 84}, "end": {"line": 80, "column": 86}}]}, "5": {"loc": {"start": {"line": 90, "column": 11}, "end": {"line": 145, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 11}, "end": {"line": 90, "column": 36}}, {"start": {"line": 91, "column": 12}, "end": {"line": 144, "column": null}}]}, "6": {"loc": {"start": {"line": 107, "column": 17}, "end": {"line": 110, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 17}, "end": {"line": 107, "column": 33}}, {"start": {"line": 108, "column": 18}, "end": {"line": 109, "column": null}}]}, "7": {"loc": {"start": {"line": 128, "column": 17}, "end": {"line": 131, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 128, "column": 17}, "end": {"line": 128, "column": 34}}, {"start": {"line": 129, "column": 18}, "end": {"line": 130, "column": null}}]}, "8": {"loc": {"start": {"line": 148, "column": 11}, "end": {"line": 274, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 148, "column": 11}, "end": {"line": 148, "column": 34}}, {"start": {"line": 149, "column": 12}, "end": {"line": 273, "column": null}}]}, "9": {"loc": {"start": {"line": 165, "column": 19}, "end": {"line": 166, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 165, "column": 19}, "end": {"line": 165, "column": 37}}, {"start": {"line": 166, "column": 20}, "end": {"line": 166, "column": 86}}]}, "10": {"loc": {"start": {"line": 184, "column": 19}, "end": {"line": 185, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 19}, "end": {"line": 184, "column": 35}}, {"start": {"line": 185, "column": 20}, "end": {"line": 185, "column": 84}}]}, "11": {"loc": {"start": {"line": 205, "column": 19}, "end": {"line": 206, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 205, "column": 19}, "end": {"line": 205, "column": 39}}, {"start": {"line": 206, "column": 20}, "end": {"line": 206, "column": 88}}]}, "12": {"loc": {"start": {"line": 224, "column": 19}, "end": {"line": 225, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 224, "column": 19}, "end": {"line": 224, "column": 34}}, {"start": {"line": 225, "column": 20}, "end": {"line": 225, "column": 83}}]}, "13": {"loc": {"start": {"line": 270, "column": 17}, "end": {"line": 271, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 270, "column": 17}, "end": {"line": 270, "column": 33}}, {"start": {"line": 271, "column": 18}, "end": {"line": 271, "column": 82}}]}, "14": {"loc": {"start": {"line": 284, "column": 15}, "end": {"line": 287, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 285, "column": 16}, "end": {"line": 285, "column": 40}}, {"start": {"line": 287, "column": 16}, "end": {"line": 287, "column": 83}}]}, "15": {"loc": {"start": {"line": 287, "column": 29}, "end": {"line": 287, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 287, "column": 57}, "end": {"line": 287, "column": 64}}, {"start": {"line": 287, "column": 67}, "end": {"line": 287, "column": 75}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 17, "6": 17, "7": 17, "8": 17, "9": 2, "10": 2, "11": 1, "12": 1, "13": 1, "14": 1, "15": 2, "16": 2, "17": 2, "18": 17, "19": 17, "20": 0, "21": 2, "22": 1}, "f": {"0": 17, "1": 2, "2": 0, "3": 2}, "b": {"0": [1, 1], "1": [2], "2": [17, 15], "3": [14, 3], "4": [3, 14], "5": [17, 14], "6": [14, 1], "7": [14, 1], "8": [17, 3], "9": [3, 0], "10": [3, 0], "11": [3, 0], "12": [3, 0], "13": [3, 0], "14": [2, 15], "15": [12, 3]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/data/tooltipInfo.ts": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/data/tooltipInfo.ts", "statementMap": {"0": {"start": {"line": 8, "column": 13}, "end": {"line": 172, "column": 2}}, "1": {"start": {"line": 175, "column": 30}, "end": {"line": 180, "column": 1}}, "2": {"start": {"line": 176, "column": 2}, "end": {"line": 179, "column": 4}}, "3": {"start": {"line": 175, "column": 13}, "end": {"line": 175, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 175, "column": 30}, "end": {"line": 175, "column": 31}}, "loc": {"start": {"line": 175, "column": 59}, "end": {"line": 180, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 176, "column": 9}, "end": {"line": 179, "column": 4}}, "type": "binary-expr", "locations": [{"start": {"line": 176, "column": 9}, "end": {"line": 176, "column": 25}}, {"start": {"line": 176, "column": 29}, "end": {"line": 179, "column": 4}}]}}, "s": {"0": 4, "1": 4, "2": 317, "3": 4}, "f": {"0": 317}, "b": {"0": [317, 3]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/hooks/useWealthTracker.ts": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/hooks/useWealthTracker.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 46}}, "2": {"start": {"line": 37, "column": 32}, "end": {"line": 221, "column": 1}}, "3": {"start": {"line": 38, "column": 38}, "end": {"line": 38, "column": 78}}, "4": {"start": {"line": 39, "column": 34}, "end": {"line": 39, "column": 71}}, "5": {"start": {"line": 40, "column": 40}, "end": {"line": 40, "column": 84}}, "6": {"start": {"line": 42, "column": 36}, "end": {"line": 42, "column": 51}}, "7": {"start": {"line": 43, "column": 52}, "end": {"line": 43, "column": 67}}, "8": {"start": {"line": 44, "column": 48}, "end": {"line": 44, "column": 63}}, "9": {"start": {"line": 45, "column": 40}, "end": {"line": 45, "column": 55}}, "10": {"start": {"line": 46, "column": 42}, "end": {"line": 46, "column": 57}}, "11": {"start": {"line": 48, "column": 28}, "end": {"line": 48, "column": 57}}, "12": {"start": {"line": 50, "column": 21}, "end": {"line": 52, "column": 8}}, "13": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 19}}, "14": {"start": {"line": 54, "column": 27}, "end": {"line": 75, "column": 8}}, "15": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 31}}, "16": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 19}}, "17": {"start": {"line": 58, "column": 4}, "end": {"line": 74, "column": 5}}, "18": {"start": {"line": 59, "column": 23}, "end": {"line": 59, "column": 59}}, "19": {"start": {"line": 61, "column": 6}, "end": {"line": 68, "column": 7}}, "20": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 31}}, "21": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 20}}, "22": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 67}}, "23": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 21}}, "24": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 41}}, "25": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 19}}, "26": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 34}}, "27": {"start": {"line": 77, "column": 25}, "end": {"line": 98, "column": 8}}, "28": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 29}}, "29": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 19}}, "30": {"start": {"line": 81, "column": 4}, "end": {"line": 97, "column": 5}}, "31": {"start": {"line": 82, "column": 23}, "end": {"line": 82, "column": 57}}, "32": {"start": {"line": 84, "column": 6}, "end": {"line": 91, "column": 7}}, "33": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 31}}, "34": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 20}}, "35": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 65}}, "36": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 21}}, "37": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 41}}, "38": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 19}}, "39": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 32}}, "40": {"start": {"line": 100, "column": 24}, "end": {"line": 120, "column": 8}}, "41": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 25}}, "42": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 19}}, "43": {"start": {"line": 104, "column": 4}, "end": {"line": 119, "column": 5}}, "44": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 56}}, "45": {"start": {"line": 107, "column": 6}, "end": {"line": 113, "column": 7}}, "46": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 35}}, "47": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 20}}, "48": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 63}}, "49": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 21}}, "50": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 41}}, "51": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 19}}, "52": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 28}}, "53": {"start": {"line": 122, "column": 30}, "end": {"line": 142, "column": 8}}, "54": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 26}}, "55": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 19}}, "56": {"start": {"line": 126, "column": 4}, "end": {"line": 141, "column": 5}}, "57": {"start": {"line": 127, "column": 23}, "end": {"line": 127, "column": 67}}, "58": {"start": {"line": 129, "column": 6}, "end": {"line": 135, "column": 7}}, "59": {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 38}}, "60": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 20}}, "61": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 69}}, "62": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 21}}, "63": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 41}}, "64": {"start": {"line": 138, "column": 6}, "end": {"line": 138, "column": 19}}, "65": {"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 29}}, "66": {"start": {"line": 144, "column": 25}, "end": {"line": 170, "column": 8}}, "67": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 23}}, "68": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 19}}, "69": {"start": {"line": 148, "column": 4}, "end": {"line": 169, "column": 5}}, "70": {"start": {"line": 149, "column": 23}, "end": {"line": 149, "column": 52}}, "71": {"start": {"line": 151, "column": 6}, "end": {"line": 163, "column": 7}}, "72": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 37}}, "73": {"start": {"line": 153, "column": 8}, "end": {"line": 155, "column": 9}}, "74": {"start": {"line": 154, "column": 10}, "end": {"line": 154, "column": 46}}, "75": {"start": {"line": 156, "column": 8}, "end": {"line": 158, "column": 9}}, "76": {"start": {"line": 157, "column": 10}, "end": {"line": 157, "column": 52}}, "77": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 20}}, "78": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 65}}, "79": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 21}}, "80": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 41}}, "81": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 19}}, "82": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 26}}, "83": {"start": {"line": 172, "column": 20}, "end": {"line": 194, "column": 8}}, "84": {"start": {"line": 173, "column": 4}, "end": {"line": 173, "column": 23}}, "85": {"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 19}}, "86": {"start": {"line": 176, "column": 4}, "end": {"line": 193, "column": 5}}, "87": {"start": {"line": 177, "column": 23}, "end": {"line": 177, "column": 48}}, "88": {"start": {"line": 179, "column": 6}, "end": {"line": 187, "column": 7}}, "89": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 28}}, "90": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 26}}, "91": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 29}}, "92": {"start": {"line": 183, "column": 8}, "end": {"line": 183, "column": 20}}, "93": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 59}}, "94": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 21}}, "95": {"start": {"line": 189, "column": 6}, "end": {"line": 189, "column": 41}}, "96": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 19}}, "97": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 26}}, "98": {"start": {"line": 196, "column": 2}, "end": {"line": 220, "column": 4}}, "99": {"start": {"line": 37, "column": 13}, "end": {"line": 37, "column": 32}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 37, "column": 32}, "end": {"line": 37, "column": 59}}, "loc": {"start": {"line": 37, "column": 61}, "end": {"line": 221, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 50, "column": 33}, "end": {"line": 50, "column": 36}}, "loc": {"start": {"line": 50, "column": 38}, "end": {"line": 52, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 39}, "end": {"line": 54, "column": 44}}, "loc": {"start": {"line": 54, "column": 87}, "end": {"line": 75, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 77, "column": 37}, "end": {"line": 77, "column": 42}}, "loc": {"start": {"line": 77, "column": 83}, "end": {"line": 98, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 100, "column": 36}, "end": {"line": 100, "column": 41}}, "loc": {"start": {"line": 100, "column": 65}, "end": {"line": 120, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 122, "column": 42}, "end": {"line": 122, "column": 47}}, "loc": {"start": {"line": 122, "column": 84}, "end": {"line": 142, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 144, "column": 37}, "end": {"line": 144, "column": 42}}, "loc": {"start": {"line": 144, "column": 66}, "end": {"line": 170, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 172, "column": 32}, "end": {"line": 172, "column": 37}}, "loc": {"start": {"line": 172, "column": 61}, "end": {"line": 194, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 61, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "if", "locations": [{"start": {"line": 61, "column": 6}, "end": {"line": 68, "column": 7}}, {"start": {"line": 65, "column": 13}, "end": {"line": 68, "column": 7}}]}, "1": {"loc": {"start": {"line": 66, "column": 17}, "end": {"line": 66, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 17}, "end": {"line": 66, "column": 31}}, {"start": {"line": 66, "column": 35}, "end": {"line": 66, "column": 65}}]}, "2": {"loc": {"start": {"line": 84, "column": 6}, "end": {"line": 91, "column": 7}}, "type": "if", "locations": [{"start": {"line": 84, "column": 6}, "end": {"line": 91, "column": 7}}, {"start": {"line": 88, "column": 13}, "end": {"line": 91, "column": 7}}]}, "3": {"loc": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 31}}, {"start": {"line": 89, "column": 35}, "end": {"line": 89, "column": 63}}]}, "4": {"loc": {"start": {"line": 107, "column": 6}, "end": {"line": 113, "column": 7}}, "type": "if", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 113, "column": 7}}, {"start": {"line": 110, "column": 13}, "end": {"line": 113, "column": 7}}]}, "5": {"loc": {"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": 26}}, {"start": {"line": 107, "column": 30}, "end": {"line": 107, "column": 43}}]}, "6": {"loc": {"start": {"line": 111, "column": 17}, "end": {"line": 111, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 17}, "end": {"line": 111, "column": 31}}, {"start": {"line": 111, "column": 35}, "end": {"line": 111, "column": 61}}]}, "7": {"loc": {"start": {"line": 129, "column": 6}, "end": {"line": 135, "column": 7}}, "type": "if", "locations": [{"start": {"line": 129, "column": 6}, "end": {"line": 135, "column": 7}}, {"start": {"line": 132, "column": 13}, "end": {"line": 135, "column": 7}}]}, "8": {"loc": {"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 26}}, {"start": {"line": 129, "column": 30}, "end": {"line": 129, "column": 43}}]}, "9": {"loc": {"start": {"line": 133, "column": 17}, "end": {"line": 133, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 17}, "end": {"line": 133, "column": 31}}, {"start": {"line": 133, "column": 35}, "end": {"line": 133, "column": 67}}]}, "10": {"loc": {"start": {"line": 151, "column": 6}, "end": {"line": 163, "column": 7}}, "type": "if", "locations": [{"start": {"line": 151, "column": 6}, "end": {"line": 163, "column": 7}}, {"start": {"line": 160, "column": 13}, "end": {"line": 163, "column": 7}}]}, "11": {"loc": {"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 26}}, {"start": {"line": 151, "column": 30}, "end": {"line": 151, "column": 43}}]}, "12": {"loc": {"start": {"line": 153, "column": 8}, "end": {"line": 155, "column": 9}}, "type": "if", "locations": [{"start": {"line": 153, "column": 8}, "end": {"line": 155, "column": 9}}]}, "13": {"loc": {"start": {"line": 156, "column": 8}, "end": {"line": 158, "column": 9}}, "type": "if", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 158, "column": 9}}]}, "14": {"loc": {"start": {"line": 161, "column": 17}, "end": {"line": 161, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 161, "column": 17}, "end": {"line": 161, "column": 31}}, {"start": {"line": 161, "column": 35}, "end": {"line": 161, "column": 63}}]}, "15": {"loc": {"start": {"line": 179, "column": 6}, "end": {"line": 187, "column": 7}}, "type": "if", "locations": [{"start": {"line": 179, "column": 6}, "end": {"line": 187, "column": 7}}, {"start": {"line": 184, "column": 13}, "end": {"line": 187, "column": 7}}]}, "16": {"loc": {"start": {"line": 185, "column": 17}, "end": {"line": 185, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 185, "column": 17}, "end": {"line": 185, "column": 31}}, {"start": {"line": 185, "column": 35}, "end": {"line": 185, "column": 57}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 23, "4": 23, "5": 23, "6": 23, "7": 23, "8": 23, "9": 23, "10": 23, "11": 23, "12": 23, "13": 0, "14": 23, "15": 4, "16": 4, "17": 4, "18": 4, "19": 4, "20": 3, "21": 3, "22": 1, "23": 1, "24": 0, "25": 0, "26": 4, "27": 23, "28": 3, "29": 3, "30": 3, "31": 3, "32": 3, "33": 2, "34": 2, "35": 1, "36": 1, "37": 0, "38": 0, "39": 3, "40": 23, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 23, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 23, "67": 6, "68": 6, "69": 6, "70": 6, "71": 6, "72": 6, "73": 6, "74": 0, "75": 6, "76": 0, "77": 6, "78": 0, "79": 0, "80": 0, "81": 0, "82": 6, "83": 23, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 23, "99": 1}, "f": {"0": 23, "1": 0, "2": 4, "3": 3, "4": 0, "5": 0, "6": 6, "7": 0}, "b": {"0": [3, 1], "1": [1, 0], "2": [2, 1], "3": [1, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [6, 0], "11": [6, 6], "12": [0], "13": [0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/services/apiService.ts": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/services/apiService.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "1": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 77}}, "2": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 25}}, "3": {"start": {"line": 16, "column": 12}, "end": {"line": 22, "column": 2}}, "4": {"start": {"line": 25, "column": 0}, "end": {"line": 34, "column": 2}}, "5": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 78}}, "6": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 18}}, "7": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 47}}, "8": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 33}}, "9": {"start": {"line": 37, "column": 23}, "end": {"line": 47, "column": 1}}, "10": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}, "11": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 54}}, "12": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "13": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 55}}, "14": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}, "15": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 54}}, "16": {"start": {"line": 49, "column": 30}, "end": {"line": 61, "column": 1}}, "17": {"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}, "18": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 50}}, "19": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "20": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 46}}, "21": {"start": {"line": 56, "column": 26}, "end": {"line": 56, "column": 90}}, "22": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "23": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 42}}, "24": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 41}}, "25": {"start": {"line": 63, "column": 28}, "end": {"line": 75, "column": 1}}, "26": {"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 3}}, "27": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 48}}, "28": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 54}}, "29": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 50}}, "30": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 58}}, "31": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 47}}, "32": {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}, "33": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 42}}, "34": {"start": {"line": 78, "column": 0}, "end": {"line": 95, "column": 2}}, "35": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 20}}, "36": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 48}}, "37": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "38": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 68}}, "39": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "40": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 63}}, "41": {"start": {"line": 90, "column": 4}, "end": {"line": 92, "column": 5}}, "42": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 72}}, "43": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 16}}, "44": {"start": {"line": 98, "column": 13}, "end": {"line": 454, "column": 2}}, "45": {"start": {"line": 101, "column": 4}, "end": {"line": 113, "column": 5}}, "46": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 47}}, "47": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 8}}, "48": {"start": {"line": 108, "column": 6}, "end": {"line": 112, "column": 8}}, "49": {"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, "50": {"start": {"line": 119, "column": 23}, "end": {"line": 119, "column": 51}}, "51": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 27}}, "52": {"start": {"line": 122, "column": 6}, "end": {"line": 126, "column": 8}}, "53": {"start": {"line": 131, "column": 4}, "end": {"line": 140, "column": 5}}, "54": {"start": {"line": 132, "column": 23}, "end": {"line": 132, "column": 59}}, "55": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 27}}, "56": {"start": {"line": 135, "column": 6}, "end": {"line": 139, "column": 8}}, "57": {"start": {"line": 144, "column": 4}, "end": {"line": 153, "column": 5}}, "58": {"start": {"line": 145, "column": 23}, "end": {"line": 145, "column": 72}}, "59": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 27}}, "60": {"start": {"line": 148, "column": 6}, "end": {"line": 152, "column": 8}}, "61": {"start": {"line": 157, "column": 4}, "end": {"line": 166, "column": 5}}, "62": {"start": {"line": 158, "column": 23}, "end": {"line": 158, "column": 84}}, "63": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 27}}, "64": {"start": {"line": 161, "column": 6}, "end": {"line": 165, "column": 8}}, "65": {"start": {"line": 170, "column": 4}, "end": {"line": 179, "column": 5}}, "66": {"start": {"line": 171, "column": 23}, "end": {"line": 171, "column": 70}}, "67": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 27}}, "68": {"start": {"line": 174, "column": 6}, "end": {"line": 178, "column": 8}}, "69": {"start": {"line": 184, "column": 4}, "end": {"line": 211, "column": 5}}, "70": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 34}}, "71": {"start": {"line": 189, "column": 26}, "end": {"line": 197, "column": 8}}, "72": {"start": {"line": 199, "column": 23}, "end": {"line": 199, "column": 68}}, "73": {"start": {"line": 200, "column": 6}, "end": {"line": 204, "column": 8}}, "74": {"start": {"line": 206, "column": 6}, "end": {"line": 210, "column": 8}}, "75": {"start": {"line": 215, "column": 4}, "end": {"line": 224, "column": 5}}, "76": {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 54}}, "77": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 27}}, "78": {"start": {"line": 219, "column": 6}, "end": {"line": 223, "column": 8}}, "79": {"start": {"line": 229, "column": 4}, "end": {"line": 254, "column": 5}}, "80": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 32}}, "81": {"start": {"line": 233, "column": 26}, "end": {"line": 240, "column": 8}}, "82": {"start": {"line": 242, "column": 23}, "end": {"line": 242, "column": 67}}, "83": {"start": {"line": 243, "column": 6}, "end": {"line": 247, "column": 8}}, "84": {"start": {"line": 249, "column": 6}, "end": {"line": 253, "column": 8}}, "85": {"start": {"line": 258, "column": 4}, "end": {"line": 268, "column": 5}}, "86": {"start": {"line": 259, "column": 6}, "end": {"line": 259, "column": 46}}, "87": {"start": {"line": 260, "column": 23}, "end": {"line": 260, "column": 68}}, "88": {"start": {"line": 261, "column": 6}, "end": {"line": 261, "column": 27}}, "89": {"start": {"line": 263, "column": 6}, "end": {"line": 267, "column": 8}}, "90": {"start": {"line": 273, "column": 4}, "end": {"line": 303, "column": 5}}, "91": {"start": {"line": 274, "column": 23}, "end": {"line": 274, "column": 59}}, "92": {"start": {"line": 275, "column": 6}, "end": {"line": 295, "column": 7}}, "93": {"start": {"line": 277, "column": 28}, "end": {"line": 277, "column": 46}}, "94": {"start": {"line": 278, "column": 49}, "end": {"line": 290, "column": 10}}, "95": {"start": {"line": 291, "column": 8}, "end": {"line": 294, "column": 10}}, "96": {"start": {"line": 296, "column": 6}, "end": {"line": 296, "column": 27}}, "97": {"start": {"line": 298, "column": 6}, "end": {"line": 302, "column": 8}}, "98": {"start": {"line": 307, "column": 4}, "end": {"line": 334, "column": 5}}, "99": {"start": {"line": 308, "column": 6}, "end": {"line": 308, "column": 44}}, "100": {"start": {"line": 309, "column": 23}, "end": {"line": 309, "column": 109}}, "101": {"start": {"line": 310, "column": 6}, "end": {"line": 326, "column": 7}}, "102": {"start": {"line": 312, "column": 51}, "end": {"line": 312, "column": 53}}, "103": {"start": {"line": 313, "column": 8}, "end": {"line": 321, "column": 9}}, "104": {"start": {"line": 313, "column": 24}, "end": {"line": 313, "column": 25}}, "105": {"start": {"line": 314, "column": 10}, "end": {"line": 320, "column": 13}}, "106": {"start": {"line": 322, "column": 8}, "end": {"line": 325, "column": 10}}, "107": {"start": {"line": 327, "column": 6}, "end": {"line": 327, "column": 27}}, "108": {"start": {"line": 329, "column": 6}, "end": {"line": 333, "column": 8}}, "109": {"start": {"line": 339, "column": 4}, "end": {"line": 360, "column": 5}}, "110": {"start": {"line": 340, "column": 51}, "end": {"line": 343, "column": 8}}, "111": {"start": {"line": 345, "column": 44}, "end": {"line": 348, "column": 8}}, "112": {"start": {"line": 350, "column": 6}, "end": {"line": 353, "column": 8}}, "113": {"start": {"line": 355, "column": 6}, "end": {"line": 359, "column": 8}}, "114": {"start": {"line": 365, "column": 4}, "end": {"line": 374, "column": 5}}, "115": {"start": {"line": 366, "column": 23}, "end": {"line": 366, "column": 56}}, "116": {"start": {"line": 367, "column": 6}, "end": {"line": 367, "column": 27}}, "117": {"start": {"line": 369, "column": 6}, "end": {"line": 373, "column": 8}}, "118": {"start": {"line": 387, "column": 4}, "end": {"line": 452, "column": 5}}, "119": {"start": {"line": 389, "column": 6}, "end": {"line": 391, "column": 7}}, "120": {"start": {"line": 390, "column": 8}, "end": {"line": 390, "column": 51}}, "121": {"start": {"line": 393, "column": 6}, "end": {"line": 393, "column": 76}}, "122": {"start": {"line": 395, "column": 6}, "end": {"line": 397, "column": 7}}, "123": {"start": {"line": 396, "column": 8}, "end": {"line": 396, "column": 64}}, "124": {"start": {"line": 399, "column": 6}, "end": {"line": 401, "column": 7}}, "125": {"start": {"line": 400, "column": 8}, "end": {"line": 400, "column": 51}}, "126": {"start": {"line": 403, "column": 6}, "end": {"line": 405, "column": 7}}, "127": {"start": {"line": 404, "column": 8}, "end": {"line": 404, "column": 79}}, "128": {"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}, "129": {"start": {"line": 408, "column": 8}, "end": {"line": 408, "column": 66}}, "130": {"start": {"line": 411, "column": 23}, "end": {"line": 418, "column": 8}}, "131": {"start": {"line": 420, "column": 6}, "end": {"line": 443, "column": 7}}, "132": {"start": {"line": 422, "column": 28}, "end": {"line": 422, "column": 30}}, "133": {"start": {"line": 423, "column": 28}, "end": {"line": 423, "column": 45}}, "134": {"start": {"line": 424, "column": 27}, "end": {"line": 424, "column": 34}}, "135": {"start": {"line": 426, "column": 8}, "end": {"line": 437, "column": 9}}, "136": {"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 26}}, "137": {"start": {"line": 427, "column": 10}, "end": {"line": 427, "column": 105}}, "138": {"start": {"line": 428, "column": 10}, "end": {"line": 431, "column": 13}}, "139": {"start": {"line": 434, "column": 10}, "end": {"line": 436, "column": 11}}, "140": {"start": {"line": 435, "column": 12}, "end": {"line": 435, "column": 18}}, "141": {"start": {"line": 439, "column": 8}, "end": {"line": 442, "column": 10}}, "142": {"start": {"line": 445, "column": 6}, "end": {"line": 445, "column": 27}}, "143": {"start": {"line": 447, "column": 6}, "end": {"line": 451, "column": 8}}, "144": {"start": {"line": 457, "column": 0}, "end": {"line": 457, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 26, "column": 13}, "end": {"line": 29, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 3}}, "loc": {"start": {"line": 30, "column": 12}, "end": {"line": 33, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 24}}, "loc": {"start": {"line": 37, "column": 86}, "end": {"line": 47, "column": 1}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 49, "column": 30}, "end": {"line": 49, "column": 31}}, "loc": {"start": {"line": 49, "column": 50}, "end": {"line": 61, "column": 1}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 63, "column": 28}, "end": {"line": 63, "column": 29}}, "loc": {"start": {"line": 63, "column": 48}, "end": {"line": 75, "column": 1}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 3}}, "loc": {"start": {"line": 79, "column": 15}, "end": {"line": 81, "column": 3}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 3}}, "loc": {"start": {"line": 82, "column": 12}, "end": {"line": 94, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 100, "column": 15}, "end": {"line": 100, "column": 20}}, "loc": {"start": {"line": 100, "column": 53}, "end": {"line": 114, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 18}}, "loc": {"start": {"line": 117, "column": 51}, "end": {"line": 128, "column": 3}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 130, "column": 16}, "end": {"line": 130, "column": 21}}, "loc": {"start": {"line": 130, "column": 66}, "end": {"line": 141, "column": 3}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 143, "column": 20}, "end": {"line": 143, "column": 25}}, "loc": {"start": {"line": 143, "column": 76}, "end": {"line": 154, "column": 3}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 156, "column": 27}, "end": {"line": 156, "column": 32}}, "loc": {"start": {"line": 156, "column": 89}, "end": {"line": 167, "column": 3}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 169, "column": 20}, "end": {"line": 169, "column": 25}}, "loc": {"start": {"line": 169, "column": 73}, "end": {"line": 180, "column": 3}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 183, "column": 20}, "end": {"line": 183, "column": 25}}, "loc": {"start": {"line": 183, "column": 87}, "end": {"line": 212, "column": 3}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 214, "column": 20}, "end": {"line": 214, "column": 25}}, "loc": {"start": {"line": 214, "column": 60}, "end": {"line": 225, "column": 3}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 228, "column": 18}, "end": {"line": 228, "column": 23}}, "loc": {"start": {"line": 228, "column": 81}, "end": {"line": 255, "column": 3}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 257, "column": 18}, "end": {"line": 257, "column": 23}}, "loc": {"start": {"line": 257, "column": 76}, "end": {"line": 269, "column": 3}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 272, "column": 21}, "end": {"line": 272, "column": 26}}, "loc": {"start": {"line": 272, "column": 70}, "end": {"line": 304, "column": 3}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 306, "column": 27}, "end": {"line": 306, "column": 32}}, "loc": {"start": {"line": 306, "column": 96}, "end": {"line": 335, "column": 3}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 338, "column": 17}, "end": {"line": 338, "column": 22}}, "loc": {"start": {"line": 338, "column": 69}, "end": {"line": 361, "column": 3}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 364, "column": 13}, "end": {"line": 364, "column": 18}}, "loc": {"start": {"line": 364, "column": 52}, "end": {"line": 375, "column": 3}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 378, "column": 24}, "end": {"line": 378, "column": 29}}, "loc": {"start": {"line": 386, "column": 71}, "end": {"line": 453, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 50}}, {"start": {"line": 12, "column": 54}, "end": {"line": 12, "column": 77}}]}, "1": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}, "type": "if", "locations": [{"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": 3}}]}, "2": {"loc": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 31}}, {"start": {"line": 38, "column": 35}, "end": {"line": 38, "column": 47}}]}, "3": {"loc": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}]}, "4": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 23}}, {"start": {"line": 41, "column": 27}, "end": {"line": 41, "column": 38}}]}, "5": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}, "type": "if", "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}]}, "6": {"loc": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 23}}, {"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 38}}]}, "7": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}, "type": "if", "locations": [{"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}]}, "8": {"loc": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 11}}, {"start": {"line": 50, "column": 15}, "end": {"line": 50, "column": 39}}]}, "9": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "type": "if", "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}]}, "10": {"loc": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 21}}, {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 59}}]}, "11": {"loc": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "type": "if", "locations": [{"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}]}, "12": {"loc": {"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 3}}, "type": "if", "locations": [{"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 3}}]}, "13": {"loc": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 11}}, {"start": {"line": 64, "column": 15}, "end": {"line": 64, "column": 39}}]}, "14": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}, "type": "if", "locations": [{"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}]}, "15": {"loc": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 21}}, {"start": {"line": 72, "column": 25}, "end": {"line": 72, "column": 85}}]}, "16": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}]}, "17": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}]}, "18": {"loc": {"start": {"line": 90, "column": 4}, "end": {"line": 92, "column": 5}}, "type": "if", "locations": [{"start": {"line": 90, "column": 4}, "end": {"line": 92, "column": 5}}]}, "19": {"loc": {"start": {"line": 111, "column": 17}, "end": {"line": 111, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 42}, "end": {"line": 111, "column": 55}}, {"start": {"line": 111, "column": 58}, "end": {"line": 111, "column": 73}}]}, "20": {"loc": {"start": {"line": 125, "column": 17}, "end": {"line": 125, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 125, "column": 42}, "end": {"line": 125, "column": 55}}, {"start": {"line": 125, "column": 58}, "end": {"line": 125, "column": 73}}]}, "21": {"loc": {"start": {"line": 138, "column": 17}, "end": {"line": 138, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 42}, "end": {"line": 138, "column": 55}}, {"start": {"line": 138, "column": 58}, "end": {"line": 138, "column": 73}}]}, "22": {"loc": {"start": {"line": 151, "column": 17}, "end": {"line": 151, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 151, "column": 42}, "end": {"line": 151, "column": 55}}, {"start": {"line": 151, "column": 58}, "end": {"line": 151, "column": 73}}]}, "23": {"loc": {"start": {"line": 164, "column": 17}, "end": {"line": 164, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 164, "column": 42}, "end": {"line": 164, "column": 55}}, {"start": {"line": 164, "column": 58}, "end": {"line": 164, "column": 73}}]}, "24": {"loc": {"start": {"line": 177, "column": 17}, "end": {"line": 177, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 177, "column": 42}, "end": {"line": 177, "column": 55}}, {"start": {"line": 177, "column": 58}, "end": {"line": 177, "column": 73}}]}, "25": {"loc": {"start": {"line": 192, "column": 14}, "end": {"line": 192, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 192, "column": 44}, "end": {"line": 192, "column": 54}}, {"start": {"line": 192, "column": 57}, "end": {"line": 192, "column": 58}}]}, "26": {"loc": {"start": {"line": 193, "column": 19}, "end": {"line": 193, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 193, "column": 49}, "end": {"line": 193, "column": 59}}, {"start": {"line": 193, "column": 62}, "end": {"line": 193, "column": 63}}]}, "27": {"loc": {"start": {"line": 194, "column": 16}, "end": {"line": 194, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 194, "column": 45}, "end": {"line": 194, "column": 55}}, {"start": {"line": 194, "column": 58}, "end": {"line": 194, "column": 59}}]}, "28": {"loc": {"start": {"line": 195, "column": 18}, "end": {"line": 195, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 195, "column": 53}, "end": {"line": 195, "column": 63}}, {"start": {"line": 195, "column": 66}, "end": {"line": 195, "column": 67}}]}, "29": {"loc": {"start": {"line": 196, "column": 18}, "end": {"line": 196, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 196, "column": 49}, "end": {"line": 196, "column": 59}}, {"start": {"line": 196, "column": 62}, "end": {"line": 196, "column": 63}}]}, "30": {"loc": {"start": {"line": 209, "column": 17}, "end": {"line": 209, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 42}, "end": {"line": 209, "column": 55}}, {"start": {"line": 209, "column": 58}, "end": {"line": 209, "column": 73}}]}, "31": {"loc": {"start": {"line": 222, "column": 17}, "end": {"line": 222, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 222, "column": 42}, "end": {"line": 222, "column": 55}}, {"start": {"line": 222, "column": 58}, "end": {"line": 222, "column": 73}}]}, "32": {"loc": {"start": {"line": 252, "column": 17}, "end": {"line": 252, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 252, "column": 42}, "end": {"line": 252, "column": 55}}, {"start": {"line": 252, "column": 58}, "end": {"line": 252, "column": 73}}]}, "33": {"loc": {"start": {"line": 257, "column": 25}, "end": {"line": 257, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 257, "column": 41}, "end": {"line": 257, "column": 43}}]}, "34": {"loc": {"start": {"line": 266, "column": 17}, "end": {"line": 266, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 266, "column": 42}, "end": {"line": 266, "column": 55}}, {"start": {"line": 266, "column": 58}, "end": {"line": 266, "column": 73}}]}, "35": {"loc": {"start": {"line": 275, "column": 6}, "end": {"line": 295, "column": 7}}, "type": "if", "locations": [{"start": {"line": 275, "column": 6}, "end": {"line": 295, "column": 7}}]}, "36": {"loc": {"start": {"line": 301, "column": 17}, "end": {"line": 301, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 301, "column": 42}, "end": {"line": 301, "column": 55}}, {"start": {"line": 301, "column": 58}, "end": {"line": 301, "column": 73}}]}, "37": {"loc": {"start": {"line": 310, "column": 6}, "end": {"line": 326, "column": 7}}, "type": "if", "locations": [{"start": {"line": 310, "column": 6}, "end": {"line": 326, "column": 7}}]}, "38": {"loc": {"start": {"line": 332, "column": 17}, "end": {"line": 332, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 332, "column": 42}, "end": {"line": 332, "column": 55}}, {"start": {"line": 332, "column": 58}, "end": {"line": 332, "column": 73}}]}, "39": {"loc": {"start": {"line": 358, "column": 17}, "end": {"line": 358, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 358, "column": 42}, "end": {"line": 358, "column": 55}}, {"start": {"line": 358, "column": 58}, "end": {"line": 358, "column": 73}}]}, "40": {"loc": {"start": {"line": 372, "column": 17}, "end": {"line": 372, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 372, "column": 42}, "end": {"line": 372, "column": 55}}, {"start": {"line": 372, "column": 58}, "end": {"line": 372, "column": 73}}]}, "41": {"loc": {"start": {"line": 389, "column": 6}, "end": {"line": 391, "column": 7}}, "type": "if", "locations": [{"start": {"line": 389, "column": 6}, "end": {"line": 391, "column": 7}}]}, "42": {"loc": {"start": {"line": 389, "column": 10}, "end": {"line": 389, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 389, "column": 10}, "end": {"line": 389, "column": 17}}, {"start": {"line": 389, "column": 21}, "end": {"line": 389, "column": 47}}]}, "43": {"loc": {"start": {"line": 395, "column": 6}, "end": {"line": 397, "column": 7}}, "type": "if", "locations": [{"start": {"line": 395, "column": 6}, "end": {"line": 397, "column": 7}}]}, "44": {"loc": {"start": {"line": 399, "column": 6}, "end": {"line": 401, "column": 7}}, "type": "if", "locations": [{"start": {"line": 399, "column": 6}, "end": {"line": 401, "column": 7}}]}, "45": {"loc": {"start": {"line": 403, "column": 6}, "end": {"line": 405, "column": 7}}, "type": "if", "locations": [{"start": {"line": 403, "column": 6}, "end": {"line": 405, "column": 7}}]}, "46": {"loc": {"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}, "type": "if", "locations": [{"start": {"line": 407, "column": 6}, "end": {"line": 409, "column": 7}}]}, "47": {"loc": {"start": {"line": 420, "column": 6}, "end": {"line": 443, "column": 7}}, "type": "if", "locations": [{"start": {"line": 420, "column": 6}, "end": {"line": 443, "column": 7}}]}, "48": {"loc": {"start": {"line": 426, "column": 38}, "end": {"line": 426, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 426, "column": 38}, "end": {"line": 426, "column": 50}}, {"start": {"line": 426, "column": 54}, "end": {"line": 426, "column": 56}}]}, "49": {"loc": {"start": {"line": 427, "column": 46}, "end": {"line": 427, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 427, "column": 46}, "end": {"line": 427, "column": 57}}, {"start": {"line": 427, "column": 61}, "end": {"line": 427, "column": 62}}]}, "50": {"loc": {"start": {"line": 434, "column": 10}, "end": {"line": 436, "column": 11}}, "type": "if", "locations": [{"start": {"line": 434, "column": 10}, "end": {"line": 436, "column": 11}}]}, "51": {"loc": {"start": {"line": 434, "column": 14}, "end": {"line": 434, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 434, "column": 14}, "end": {"line": 434, "column": 27}}, {"start": {"line": 434, "column": 31}, "end": {"line": 434, "column": 60}}]}, "52": {"loc": {"start": {"line": 450, "column": 17}, "end": {"line": 450, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 450, "column": 42}, "end": {"line": 450, "column": 55}}, {"start": {"line": 450, "column": 58}, "end": {"line": 450, "column": 73}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0], "12": [0], "13": [0, 0], "14": [0], "15": [0, 0], "16": [0], "17": [0], "18": [0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0], "34": [0, 0], "35": [0], "36": [0, 0], "37": [0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0], "42": [0, 0], "43": [0], "44": [0], "45": [0], "46": [0], "47": [0], "48": [0, 0], "49": [0, 0], "50": [0], "51": [0, 0], "52": [0, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/services/mockApi.ts": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/services/mockApi.ts", "statementMap": {"0": {"start": {"line": 11, "column": 40}, "end": {"line": 27, "column": 2}}, "1": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 77}}, "2": {"start": {"line": 30, "column": 30}, "end": {"line": 30, "column": 77}}, "3": {"start": {"line": 30, "column": 53}, "end": {"line": 30, "column": 76}}, "4": {"start": {"line": 33, "column": 13}, "end": {"line": 204, "column": 2}}, "5": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 21}}, "6": {"start": {"line": 38, "column": 4}, "end": {"line": 54, "column": 5}}, "7": {"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 9}}, "8": {"start": {"line": 44, "column": 6}, "end": {"line": 48, "column": 8}}, "9": {"start": {"line": 50, "column": 6}, "end": {"line": 53, "column": 8}}, "10": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 21}}, "11": {"start": {"line": 61, "column": 4}, "end": {"line": 77, "column": 5}}, "12": {"start": {"line": 62, "column": 6}, "end": {"line": 65, "column": 9}}, "13": {"start": {"line": 67, "column": 6}, "end": {"line": 71, "column": 8}}, "14": {"start": {"line": 73, "column": 6}, "end": {"line": 76, "column": 8}}, "15": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 21}}, "16": {"start": {"line": 84, "column": 4}, "end": {"line": 129, "column": 5}}, "17": {"start": {"line": 85, "column": 26}, "end": {"line": 86, "column": null}}, "18": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 41}}, "19": {"start": {"line": 89, "column": 28}, "end": {"line": 98, "column": 43}}, "20": {"start": {"line": 91, "column": 26}, "end": {"line": 95, "column": 35}}, "21": {"start": {"line": 96, "column": 10}, "end": {"line": 96, "column": 31}}, "22": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 45}}, "23": {"start": {"line": 102, "column": 29}, "end": {"line": 106, "column": null}}, "24": {"start": {"line": 104, "column": 10}, "end": {"line": 104, "column": 87}}, "25": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 27}}, "26": {"start": {"line": 109, "column": 39}, "end": {"line": 116, "column": 8}}, "27": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 41}}, "28": {"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 8}}, "29": {"start": {"line": 125, "column": 6}, "end": {"line": 128, "column": 8}}, "30": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 22}}, "31": {"start": {"line": 136, "column": 4}, "end": {"line": 177, "column": 5}}, "32": {"start": {"line": 137, "column": 21}, "end": {"line": 137, "column": 44}}, "33": {"start": {"line": 138, "column": 6}, "end": {"line": 144, "column": 7}}, "34": {"start": {"line": 139, "column": 33}, "end": {"line": 139, "column": 66}}, "35": {"start": {"line": 140, "column": 8}, "end": {"line": 142, "column": 9}}, "36": {"start": {"line": 141, "column": 10}, "end": {"line": 141, "column": 57}}, "37": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 41}}, "38": {"start": {"line": 146, "column": 49}, "end": {"line": 146, "column": 51}}, "39": {"start": {"line": 148, "column": 6}, "end": {"line": 164, "column": 7}}, "40": {"start": {"line": 148, "column": 22}, "end": {"line": 148, "column": 23}}, "41": {"start": {"line": 149, "column": 27}, "end": {"line": 149, "column": 31}}, "42": {"start": {"line": 150, "column": 30}, "end": {"line": 150, "column": 34}}, "43": {"start": {"line": 152, "column": 32}, "end": {"line": 152, "column": 85}}, "44": {"start": {"line": 153, "column": 32}, "end": {"line": 153, "column": 89}}, "45": {"start": {"line": 154, "column": 33}, "end": {"line": 154, "column": 71}}, "46": {"start": {"line": 155, "column": 34}, "end": {"line": 155, "column": 68}}, "47": {"start": {"line": 157, "column": 8}, "end": {"line": 163, "column": 11}}, "48": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 47}}, "49": {"start": {"line": 168, "column": 6}, "end": {"line": 171, "column": 8}}, "50": {"start": {"line": 173, "column": 6}, "end": {"line": 176, "column": 8}}, "51": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 21}}, "52": {"start": {"line": 184, "column": 4}, "end": {"line": 187, "column": 6}}, "53": {"start": {"line": 192, "column": 4}, "end": {"line": 192, "column": 21}}, "54": {"start": {"line": 194, "column": 4}, "end": {"line": 197, "column": 6}}, "55": {"start": {"line": 199, "column": 4}, "end": {"line": 202, "column": 6}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 15}}, "loc": {"start": {"line": 30, "column": 30}, "end": {"line": 30, "column": 77}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 30, "column": 42}, "end": {"line": 30, "column": 49}}, "loc": {"start": {"line": 30, "column": 53}, "end": {"line": 30, "column": 76}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 35, "column": 20}, "end": {"line": 35, "column": 25}}, "loc": {"start": {"line": 35, "column": 87}, "end": {"line": 55, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 58, "column": 18}, "end": {"line": 58, "column": 23}}, "loc": {"start": {"line": 58, "column": 81}, "end": {"line": 78, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 26}}, "loc": {"start": {"line": 81, "column": 70}, "end": {"line": 130, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 9}}, "loc": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 41}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 9}}, "loc": {"start": {"line": 90, "column": 25}, "end": {"line": 97, "column": 9}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 9}}, "loc": {"start": {"line": 103, "column": 29}, "end": {"line": 106, "column": 9}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 133, "column": 27}, "end": {"line": 133, "column": 32}}, "loc": {"start": {"line": 133, "column": 96}, "end": {"line": 178, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 181, "column": 17}, "end": {"line": 181, "column": 22}}, "loc": {"start": {"line": 181, "column": 69}, "end": {"line": 188, "column": 3}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 191, "column": 13}, "end": {"line": 191, "column": 18}}, "loc": {"start": {"line": 191, "column": 52}, "end": {"line": 203, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 91, "column": 26}, "end": {"line": 95, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 14}, "end": {"line": 92, "column": 31}}, {"start": {"line": 93, "column": 14}, "end": {"line": 95, "column": 35}}]}, "1": {"loc": {"start": {"line": 93, "column": 14}, "end": {"line": 95, "column": 35}}, "type": "cond-expr", "locations": [{"start": {"line": 94, "column": 14}, "end": {"line": 94, "column": 35}}, {"start": {"line": 95, "column": 14}, "end": {"line": 95, "column": 35}}]}, "2": {"loc": {"start": {"line": 104, "column": 40}, "end": {"line": 104, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 104, "column": 40}, "end": {"line": 104, "column": 66}}, {"start": {"line": 104, "column": 70}, "end": {"line": 104, "column": 71}}]}, "3": {"loc": {"start": {"line": 138, "column": 6}, "end": {"line": 144, "column": 7}}, "type": "if", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 144, "column": 7}}]}, "4": {"loc": {"start": {"line": 140, "column": 8}, "end": {"line": 142, "column": 9}}, "type": "if", "locations": [{"start": {"line": 140, "column": 8}, "end": {"line": 142, "column": 9}}]}, "5": {"loc": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 37}}, {"start": {"line": 140, "column": 41}, "end": {"line": 140, "column": 63}}]}}, "s": {"0": 2, "1": 2, "2": 30, "3": 30, "4": 2, "5": 7, "6": 7, "7": 7, "8": 7, "9": 0, "10": 5, "11": 5, "12": 5, "13": 5, "14": 0, "15": 5, "16": 5, "17": 5, "18": 25, "19": 5, "20": 14, "21": 14, "22": 5, "23": 5, "24": 25, "25": 25, "26": 5, "27": 5, "28": 5, "29": 0, "30": 7, "31": 7, "32": 7, "33": 7, "34": 0, "35": 0, "36": 0, "37": 0, "38": 7, "39": 7, "40": 7, "41": 30, "42": 30, "43": 30, "44": 30, "45": 30, "46": 30, "47": 30, "48": 7, "49": 7, "50": 0, "51": 6, "52": 6, "53": 0, "54": 0, "55": 0}, "f": {"0": 30, "1": 30, "2": 7, "3": 5, "4": 5, "5": 25, "6": 14, "7": 25, "8": 7, "9": 6, "10": 0}, "b": {"0": [10, 4], "1": [0, 4], "2": [25, 20], "3": [0], "4": [0], "5": [0, 0]}}, "/Users/<USER>/WebstormProjects/er/frontend/src/utils/payslipGenerator.ts": {"path": "/Users/<USER>/WebstormProjects/er/frontend/src/utils/payslipGenerator.ts", "statementMap": {"0": {"start": {"line": 34, "column": 8}, "end": {"line": 34, "column": 14}}, "1": {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 87}}, "2": {"start": {"line": 41, "column": 24}, "end": {"line": 41, "column": 107}}, "3": {"start": {"line": 43, "column": 45}, "end": {"line": 43, "column": 47}}, "4": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 31}}, "5": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 58}}, "6": {"start": {"line": 47, "column": 4}, "end": {"line": 69, "column": 5}}, "7": {"start": {"line": 47, "column": 17}, "end": {"line": 47, "column": 18}}, "8": {"start": {"line": 48, "column": 24}, "end": {"line": 48, "column": 42}}, "9": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 50}}, "10": {"start": {"line": 52, "column": 24}, "end": {"line": 52, "column": 52}}, "11": {"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 67}}, "12": {"start": {"line": 54, "column": 25}, "end": {"line": 54, "column": 44}}, "13": {"start": {"line": 55, "column": 28}, "end": {"line": 55, "column": 47}}, "14": {"start": {"line": 56, "column": 23}, "end": {"line": 56, "column": 42}}, "15": {"start": {"line": 58, "column": 6}, "end": {"line": 68, "column": 9}}, "16": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 20}}, "17": {"start": {"line": 79, "column": 36}, "end": {"line": 88, "column": 6}}, "18": {"start": {"line": 91, "column": 17}, "end": {"line": 91, "column": 87}}, "19": {"start": {"line": 91, "column": 81}, "end": {"line": 91, "column": 86}}, "20": {"start": {"line": 92, "column": 21}, "end": {"line": 92, "column": 28}}, "21": {"start": {"line": 94, "column": 4}, "end": {"line": 98, "column": 5}}, "22": {"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}, "23": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 28}}, "24": {"start": {"line": 100, "column": 26}, "end": {"line": 100, "column": 101}}, "25": {"start": {"line": 101, "column": 28}, "end": {"line": 101, "column": 58}}, "26": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 62}}, "27": {"start": {"line": 115, "column": 29}, "end": {"line": 120, "column": 6}}, "28": {"start": {"line": 122, "column": 22}, "end": {"line": 122, "column": 51}}, "29": {"start": {"line": 125, "column": 4}, "end": {"line": 129, "column": 5}}, "30": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 26}}, "31": {"start": {"line": 127, "column": 11}, "end": {"line": 129, "column": 5}}, "32": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 26}}, "33": {"start": {"line": 132, "column": 35}, "end": {"line": 132, "column": 64}}, "34": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "35": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 26}}, "36": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 55}}, "37": {"start": {"line": 147, "column": 18}, "end": {"line": 147, "column": 50}}, "38": {"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, "39": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 20}}, "40": {"start": {"line": 152, "column": 11}, "end": {"line": 156, "column": 5}}, "41": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 22}}, "42": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 19}}, "43": {"start": {"line": 163, "column": 4}, "end": {"line": 206, "column": 6}}, "44": {"start": {"line": 213, "column": 4}, "end": {"line": 218, "column": 7}}, "45": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 13}}, "46": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 32}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 8}}, "loc": {"start": {"line": 28, "column": 66}, "end": {"line": 72, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": 16}}, "loc": {"start": {"line": 77, "column": 73}, "end": {"line": 105, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 91, "column": 71}, "end": {"line": 91, "column": 72}}, "loc": {"start": {"line": 91, "column": 81}, "end": {"line": 91, "column": 86}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 110, "column": 10}, "end": {"line": 110, "column": 16}}, "loc": {"start": {"line": 113, "column": 57}, "end": {"line": 138, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 143, "column": 10}, "end": {"line": 143, "column": 16}}, "loc": {"start": {"line": 145, "column": 27}, "end": {"line": 157, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 162, "column": 2}, "end": {"line": 162, "column": 8}}, "loc": {"start": {"line": 162, "column": 39}, "end": {"line": 207, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 8}}, "loc": {"start": {"line": 212, "column": 45}, "end": {"line": 219, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 23}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 23}}]}, "1": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 21}}]}, "2": {"loc": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 33, "column": 27}, "end": {"line": 33, "column": 32}}]}, "3": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}]}, "4": {"loc": {"start": {"line": 125, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 125, "column": 4}, "end": {"line": 129, "column": 5}}, {"start": {"line": 127, "column": 11}, "end": {"line": 129, "column": 5}}]}, "5": {"loc": {"start": {"line": 127, "column": 11}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 11}, "end": {"line": 129, "column": 5}}]}, "6": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}]}, "7": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, {"start": {"line": 152, "column": 11}, "end": {"line": 156, "column": 5}}]}, "8": {"loc": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 18}}, {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 33}}]}, "9": {"loc": {"start": {"line": 152, "column": 11}, "end": {"line": 156, "column": 5}}, "type": "if", "locations": [{"start": {"line": 152, "column": 11}, "end": {"line": 156, "column": 5}}, {"start": {"line": 154, "column": 11}, "end": {"line": 156, "column": 5}}]}, "10": {"loc": {"start": {"line": 152, "column": 15}, "end": {"line": 152, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 15}, "end": {"line": 152, "column": 27}}, {"start": {"line": 152, "column": 31}, "end": {"line": 152, "column": 42}}]}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 138, "9": 138, "10": 138, "11": 138, "12": 138, "13": 138, "14": 138, "15": 138, "16": 13, "17": 13, "18": 13, "19": 91, "20": 13, "21": 13, "22": 104, "23": 30, "24": 13, "25": 13, "26": 13, "27": 13, "28": 13, "29": 13, "30": 9, "31": 4, "32": 2, "33": 13, "34": 13, "35": 4, "36": 13, "37": 138, "38": 138, "39": 102, "40": 36, "41": 12, "42": 24, "43": 1, "44": 1, "45": 1, "46": 1}, "f": {"0": 13, "1": 13, "2": 91, "3": 13, "4": 138, "5": 1, "6": 1}, "b": {"0": [8], "1": [3], "2": [9], "3": [30], "4": [9, 4], "5": [2], "6": [4], "7": [102, 36], "8": [138, 126], "9": [12, 24], "10": [36, 24]}}}