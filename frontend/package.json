{"name": "wealth-tracker-frontend", "version": "1.0.0", "description": "Beautiful wealth tracker frontend with Liquid Glass UI", "main": "index.js", "scripts": {"start": "webpack serve --mode development --open", "build": "webpack --mode production", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "keywords": ["react", "typescript", "wealth-tracker"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.7", "d3": "^7.8.5", "liquid-glass-react": "^1.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.0", "react-table": "^7.8.0", "recharts": "^2.12.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.2", "@types/d3": "^7.4.0", "@types/jest": "^29.5.12", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-table": "^7.7.14", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "babel-loader": "^9.1.3", "css-loader": "^6.10.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "react-refresh": "^0.17.0", "style-loader": "^3.3.4", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.2"}}