// Frontend types based on backend services

export interface PortfolioData {
  assetType: 'stocks' | 'bonds' | 'crypto' | 'real_estate' | 'savings' | 'other';
  value: number;
  description?: string;
  dateAdded?: Date;
}

export interface PayslipData {
  grossSalary: number;
  netSalary: number;
  taxDeductions: number;
  benefits: number;
  bonuses?: number;
  stockOptions?: number;
  payPeriod: 'monthly' | 'bi-weekly' | 'weekly';
  dateAdded?: Date;
}

export interface WealthAnalysis {
  totalAssets: number;
  monthlyIncome: number;
  annualIncome: number;
  savingsRate: number;
  netWorth: number;
  assetBreakdown: {
    [key: string]: number;
  };
}

export interface FinancialProjection {
  year: number;
  projectedWealth: number;
  projectedIncome: number;
  projectedSavings: number;
  projectedExpenses: number;
}

export interface WealthTrackerData {
  portfolioData: PortfolioData[];
  payslipData: PayslipData[];
  analysis?: WealthAnalysis;
  projections?: FinancialProjection[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface FormData {
  portfolioData: PortfolioData;
  payslipData: PayslipData;
}