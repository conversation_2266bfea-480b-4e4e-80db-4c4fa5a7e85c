/* Global styles for Wealth Tracker with Liquid Glass design */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #ffffff;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Liquid Glass SVG Filters */
.glass-filters {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
}

/* Glass morphism base styles */
.glass-card {
  --bg-color: rgba(255, 255, 255, 0.25);
  --highlight: rgba(255, 255, 255, 0.75);
  --text: #ffffff;
  
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--bg-color);
  z-index: 1;
}

.glass-card::after {
  content: '';
  position: absolute;
  inset: 0;
  box-shadow: inset 1px 1px 1px var(--highlight);
  z-index: 2;
  pointer-events: none;
}

.glass-content {
  position: relative;
  z-index: 3;
  padding: 20px;
  color: var(--text);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .glass-card {
    --bg-color: rgba(0, 0, 0, 0.25);
    --highlight: rgba(255, 255, 255, 0.15);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .glass-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .glass-content {
    padding: 12px;
  }
}