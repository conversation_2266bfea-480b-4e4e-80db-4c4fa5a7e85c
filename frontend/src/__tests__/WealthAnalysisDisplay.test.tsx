import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import WealthAnalysisDisplay from '../components/WealthAnalysisDisplay';
import { WealthAnalysis } from '../types';

// Mock TooltipInfo component
jest.mock('../components/TooltipInfo', () => {
  return function MockTooltipInfo({ children, content, title }: { children: React.ReactNode; content: string; title?: string }) {
    return (
      <div data-testid="tooltip-info" data-content={content} data-title={title}>
        {children}
      </div>
    );
  };
});

describe('WealthAnalysisDisplay', () => {
  const mockAnalysis: WealthAnalysis = {
    totalAssets: 500000,
    netWorth: 450000,
    monthlyIncome: 8000,
    annualIncome: 96000,
    savingsRate: 0.25,
    assetBreakdown: {
      stocks: 300000,
      bonds: 100000,
      crypto: 60000,
      savings: 40000
    }
  };

  it('renders analysis data correctly', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    expect(screen.getByText('$500,000')).toBeInTheDocument(); // Total Assets
    expect(screen.getByText('$450,000')).toBeInTheDocument(); // Net Worth
    expect(screen.getByText('$8,000')).toBeInTheDocument(); // Monthly Income
    expect(screen.getByText('$96,000')).toBeInTheDocument(); // Annual Income
    expect(screen.getByText('25.0%')).toBeInTheDocument(); // Savings Rate
  });

  it('displays asset breakdown correctly', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    expect(screen.getByText('$300,000')).toBeInTheDocument(); // Stocks
    expect(screen.getByText('$100,000')).toBeInTheDocument(); // Bonds
    expect(screen.getByText('$60,000')).toBeInTheDocument(); // Crypto
    expect(screen.getByText('$40,000')).toBeInTheDocument(); // Savings
  });

  it('shows progress bars for asset breakdown', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    // Check for progress bars with specific asset types
    expect(screen.getByTestId('asset-stocks-progress')).toBeInTheDocument();
    expect(screen.getByTestId('asset-bonds-progress')).toBeInTheDocument();
    expect(screen.getByTestId('asset-crypto-progress')).toBeInTheDocument();
    expect(screen.getByTestId('asset-savings-progress')).toBeInTheDocument();
  });

  it('calculates asset percentages correctly', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    // Stocks: 300,000 / 500,000 = 60%
    // Bonds: 100,000 / 500,000 = 20%
    // Crypto: 60,000 / 500,000 = 12%
    // Savings: 40,000 / 500,000 = 8%
    
    expect(screen.getByText('60.0%')).toBeInTheDocument();
    expect(screen.getByText('20.0%')).toBeInTheDocument();
    expect(screen.getByText('12.0%')).toBeInTheDocument();
    expect(screen.getByText('8.0%')).toBeInTheDocument();
  });

  it('handles zero values gracefully', () => {
    const zeroAnalysis: WealthAnalysis = {
      totalAssets: 0,
      netWorth: 0,
      monthlyIncome: 0,
      annualIncome: 0,
      savingsRate: 0,
      assetBreakdown: {
        stocks: 0,
        bonds: 0,
        crypto: 0,
        savings: 0
      }
    };

    render(<WealthAnalysisDisplay analysis={zeroAnalysis} />);
    
    // Check that multiple $0 values are present
    const zeroValues = screen.getAllByText('$0');
    expect(zeroValues.length).toBeGreaterThan(0);
    expect(screen.getByText('0.0%')).toBeInTheDocument();
  });

  it('handles negative values correctly', () => {
    const negativeAnalysis: WealthAnalysis = {
      totalAssets: 100000,
      netWorth: -50000,
      monthlyIncome: 5000,
      annualIncome: 60000,
      savingsRate: -0.1,
      assetBreakdown: {
        stocks: 100000,
        bonds: 0,
        crypto: 0,
        savings: 0
      }
    };

    render(<WealthAnalysisDisplay analysis={negativeAnalysis} />);
    
    expect(screen.getByText('-$50,000')).toBeInTheDocument();
    expect(screen.getByText('-10.0%')).toBeInTheDocument();
  });

  it('displays tooltips for all metrics', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    // The component should render without errors and contain the main sections
    expect(screen.getByTestId('wealth-analysis-display')).toBeInTheDocument();
    expect(screen.getByTestId('asset-breakdown')).toBeInTheDocument();
    
    // Check that key metrics are displayed
    expect(screen.getByTestId('total-assets-metric')).toBeInTheDocument();
    expect(screen.getByTestId('net-worth-metric')).toBeInTheDocument();
  });

  it('formats large numbers correctly', () => {
    const largeAnalysis: WealthAnalysis = {
      totalAssets: 1500000,
      netWorth: 1200000,
      monthlyIncome: 12000,
      annualIncome: 144000,
      savingsRate: 0.35,
      assetBreakdown: {
        stocks: 900000,
        bonds: 300000,
        crypto: 150000,
        savings: 150000
      }
    };

    render(<WealthAnalysisDisplay analysis={largeAnalysis} />);
    
    expect(screen.getByText('$1,500,000')).toBeInTheDocument();
    expect(screen.getByText('$1,200,000')).toBeInTheDocument();
    expect(screen.getByText('$12,000')).toBeInTheDocument();
    expect(screen.getByText('$144,000')).toBeInTheDocument();
    expect(screen.getByText('35.0%')).toBeInTheDocument();
  });

  it('handles decimal values correctly', () => {
    const decimalAnalysis: WealthAnalysis = {
      totalAssets: 123456.78,
      netWorth: 98765.43,
      monthlyIncome: 8765.43,
      annualIncome: 105185.16,
      savingsRate: 0.234,
      assetBreakdown: {
        stocks: 74074.07,
        bonds: 24691.36,
        crypto: 12345.68,
        savings: 12345.67
      }
    };

    render(<WealthAnalysisDisplay analysis={decimalAnalysis} />);
    
    expect(screen.getByText('$123,457')).toBeInTheDocument(); // Rounded
    expect(screen.getByText('$98,765')).toBeInTheDocument(); // Rounded
    expect(screen.getByText('23.4%')).toBeInTheDocument(); // Rounded percentage
  });

  it('shows asset breakdown section', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    expect(screen.getByText('🏆 Asset Breakdown')).toBeInTheDocument();
    expect(screen.getByText('STOCKS')).toBeInTheDocument();
    expect(screen.getByText('BONDS')).toBeInTheDocument();
    expect(screen.getByText('CRYPTO')).toBeInTheDocument();
    expect(screen.getByText('SAVINGS')).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    render(<WealthAnalysisDisplay analysis={mockAnalysis} />);
    
    // Check that the component has the correct test id and structure
    expect(screen.getByTestId('wealth-analysis-display')).toBeInTheDocument();
    expect(screen.getByTestId('asset-breakdown')).toBeInTheDocument();
  });

  it('handles edge case with very small percentages', () => {
    const smallPercentageAnalysis: WealthAnalysis = {
      totalAssets: 1000000,
      netWorth: 900000,
      monthlyIncome: 8000,
      annualIncome: 96000,
      savingsRate: 0.08,
      assetBreakdown: {
        stocks: 999000,
        bonds: 500,
        crypto: 250,
        savings: 250
      }
    };

    render(<WealthAnalysisDisplay analysis={smallPercentageAnalysis} />);
    
    expect(screen.getByText('99.9%')).toBeInTheDocument(); // Stocks (rounded)
    expect(screen.getByText('0.1%')).toBeInTheDocument(); // Very small percentages
  });
});