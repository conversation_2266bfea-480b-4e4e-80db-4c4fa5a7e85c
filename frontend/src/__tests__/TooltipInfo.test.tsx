import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TooltipInfo from '../components/TooltipInfo';

describe('TooltipInfo', () => {
  const mockTooltipData = {
    title: 'Test Title',
    content: 'This is test content for the tooltip.'
  };

  it('renders children correctly', () => {
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <span>Test Child</span>
      </TooltipInfo>
    );
    
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('shows tooltip on hover', async () => {
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <span>Hover me</span>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Hover me');
    fireEvent.mouseEnter(triggerElement);
    
    await waitFor(() => {
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.getByText('This is test content for the tooltip.')).toBeInTheDocument();
    });
  });

  it('hides tooltip on mouse leave', async () => {
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <span>Hover me</span>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Hover me');
    
    // Show tooltip
    fireEvent.mouseEnter(triggerElement);
    await waitFor(() => {
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });
    
    // Hide tooltip
    fireEvent.mouseLeave(triggerElement);
    await waitFor(() => {
      expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
    });
  });

  it('shows tooltip with just content when no title provided', async () => {
    render(
      <TooltipInfo content={mockTooltipData.content}>
        <span>Hover me</span>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Hover me');
    fireEvent.mouseEnter(triggerElement);
    
    await waitFor(() => {
      expect(screen.getByText('This is test content for the tooltip.')).toBeInTheDocument();
      expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
    });
  });

  it('handles complex tooltip content', async () => {
    const complexContent = 'This is a complex tooltip with multiple lines. It contains detailed information about financial metrics and calculations.';
    
    render(
      <TooltipInfo content={complexContent} title="Complex Tooltip">
        <button>Complex Button</button>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Complex Button');
    fireEvent.mouseEnter(triggerElement);
    
    await waitFor(() => {
      expect(screen.getByText('Complex Tooltip')).toBeInTheDocument();
      expect(screen.getByText(complexContent)).toBeInTheDocument();
    });
  });

  it('positions tooltip correctly on screen', async () => {
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <span>Positioned element</span>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Positioned element');
    fireEvent.mouseEnter(triggerElement);
    
    await waitFor(() => {
      const tooltip = screen.getByText('Test Title').closest('[data-testid="tooltip"]') || 
                     screen.getByText('Test Title').parentElement;
      expect(tooltip).toBeInTheDocument();
    });
  });

  it('handles multiple tooltip instances', async () => {
    render(
      <div>
        <TooltipInfo content="First tooltip" title="First">
          <span>First</span>
        </TooltipInfo>
        <TooltipInfo content="Second tooltip" title="Second">
          <span>Second</span>
        </TooltipInfo>
      </div>
    );
    
    const firstTrigger = screen.getByText('First');
    const secondTrigger = screen.getByText('Second');
    
    fireEvent.mouseEnter(firstTrigger);
    await waitFor(() => {
      expect(screen.getByText('First tooltip')).toBeInTheDocument();
    });
    
    fireEvent.mouseLeave(firstTrigger);
    fireEvent.mouseEnter(secondTrigger);
    
    await waitFor(() => {
      expect(screen.getByText('Second tooltip')).toBeInTheDocument();
      expect(screen.queryByText('First tooltip')).not.toBeInTheDocument();
    });
  });

  it('handles click events while tooltip is visible', async () => {
    const handleClick = jest.fn();
    
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <button onClick={handleClick}>Clickable</button>
      </TooltipInfo>
    );
    
    const button = screen.getByText('Clickable');
    fireEvent.mouseEnter(button);
    
    await waitFor(() => {
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });
    
    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation', async () => {
    render(
      <TooltipInfo content={mockTooltipData.content} title={mockTooltipData.title}>
        <button>Keyboard accessible</button>
      </TooltipInfo>
    );
    
    const button = screen.getByText('Keyboard accessible');
    fireEvent.focus(button);
    
    await waitFor(() => {
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });
    
    fireEvent.blur(button);
    await waitFor(() => {
      expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
    });
  });

  it('handles long content gracefully', async () => {
    const longContent = 'This is a very long tooltip content that should be handled gracefully by the component. '.repeat(10);
    
    render(
      <TooltipInfo content={longContent} title="Long Content">
        <span>Long tooltip</span>
      </TooltipInfo>
    );
    
    const triggerElement = screen.getByText('Long tooltip');
    fireEvent.mouseEnter(triggerElement);
    
    await waitFor(() => {
      expect(screen.getByText('Long Content')).toBeInTheDocument();
      expect(screen.getByText(longContent)).toBeInTheDocument();
    });
  });
});