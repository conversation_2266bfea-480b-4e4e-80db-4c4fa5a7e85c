import { mockApi } from '../services/mockApi';
import { PortfolioData, PayslipData } from '../types';

describe('mockApi', () => {
  beforeEach(() => {
    // Reset any mock data state before each test
    jest.clearAllMocks();
  });

  describe('getWealthData', () => {
    it('returns mock wealth data successfully', async () => {
      const result = await mockApi.getWealthData();
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      if (result.success && result.data) {
        expect(result.data.portfolioData).toBeInstanceOf(Array);
        expect(result.data.payslipData).toBeInstanceOf(Array);
      }
    });

    it('returns realistic portfolio data', async () => {
      const result = await mockApi.getWealthData();
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        const portfolioData = result.data.portfolioData;
        
        if (portfolioData.length > 0) {
          const firstEntry = portfolioData[0];
          expect(firstEntry).toHaveProperty('assetType');
          expect(firstEntry).toHaveProperty('value');
          expect(firstEntry.value).toBeGreaterThan(0);
          expect(['stocks', 'bonds', 'crypto', 'real_estate', 'savings', 'other']).toContain(firstEntry.assetType);
        }
      }
    });

    it('returns realistic payslip data', async () => {
      const result = await mockApi.getWealthData();
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        const payslipData = result.data.payslipData;
        
        if (payslipData.length > 0) {
          const firstEntry = payslipData[0];
          expect(firstEntry).toHaveProperty('grossSalary');
          expect(firstEntry).toHaveProperty('netSalary');
          expect(firstEntry).toHaveProperty('benefits');
          expect(firstEntry.grossSalary).toBeGreaterThan(firstEntry.netSalary);
          expect(firstEntry.netSalary).toBeGreaterThan(0);
          expect(firstEntry.benefits).toBeGreaterThanOrEqual(0);
        }
      }
    });
  });

  describe('getWealthAnalysis', () => {
    it('returns wealth analysis successfully', async () => {
      const result = await mockApi.getWealthAnalysis();
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      if (result.success && result.data) {
        expect(result.data.totalAssets).toBeGreaterThan(0);
        expect(result.data.netWorth).toBeGreaterThan(0);
        expect(result.data.monthlyIncome).toBeGreaterThan(0);
        expect(result.data.annualIncome).toBeGreaterThan(0);
        expect(result.data.savingsRate).toBeGreaterThan(0);
        expect(result.data.savingsRate).toBeLessThan(1);
      }
    });

    it('returns consistent asset breakdown', async () => {
      const result = await mockApi.getWealthAnalysis();
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        const assetBreakdown = result.data.assetBreakdown;
        
        expect(assetBreakdown.stocks).toBeGreaterThanOrEqual(0);
        expect(assetBreakdown.bonds).toBeGreaterThanOrEqual(0);
        expect(assetBreakdown.crypto).toBeGreaterThanOrEqual(0);
        expect(assetBreakdown.savings).toBeGreaterThanOrEqual(0);
        
        const totalBreakdown = assetBreakdown.stocks + assetBreakdown.bonds + 
                              assetBreakdown.crypto + assetBreakdown.savings;
        
        // Total breakdown should approximately equal total assets
        expect(Math.abs(totalBreakdown - result.data.totalAssets)).toBeLessThan(1);
      }
    });

    it('returns realistic financial ratios', async () => {
      const result = await mockApi.getWealthAnalysis();
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        // Net worth should be less than or equal to total assets
        expect(result.data.netWorth).toBeLessThanOrEqual(result.data.totalAssets);
        
        // Annual income should be monthly income * 12
        expect(result.data.annualIncome).toBeCloseTo(result.data.monthlyIncome * 12, 0);
        
        // Savings rate should be reasonable (0-100%)
        expect(result.data.savingsRate).toBeGreaterThan(0);
        expect(result.data.savingsRate).toBeLessThan(1);
      }
    });
  });

  describe('getFinancialProjections', () => {
    it('returns financial projections successfully', async () => {
      const years = 5;
      const result = await mockApi.getFinancialProjections(years);
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data).toBeInstanceOf(Array);
        expect(result.data.length).toBe(years);
      }
    });

    it('returns realistic projection data', async () => {
      const result = await mockApi.getFinancialProjections(3);
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        result.data.forEach((projection, index) => {
          expect(projection.year).toBe(index + 1);
          expect(projection.projectedWealth).toBeGreaterThan(0);
          expect(projection.projectedIncome).toBeGreaterThan(0);
          expect(projection.projectedSavings).toBeGreaterThan(0);
          expect(projection.projectedExpenses).toBeGreaterThan(0);
        });
      }
    });

    it('shows increasing wealth over time', async () => {
      const result = await mockApi.getFinancialProjections(5);
      
      expect(result.success).toBe(true);
      if (result.success && result.data) {
        for (let i = 1; i < result.data.length; i++) {
          expect(result.data[i].projectedWealth).toBeGreaterThan(result.data[i-1].projectedWealth);
        }
      }
    });

    it('handles different projection periods', async () => {
      const shortTerm = await mockApi.getFinancialProjections(2);
      const longTerm = await mockApi.getFinancialProjections(10);
      
      expect(shortTerm.success).toBe(true);
      expect(longTerm.success).toBe(true);
      
      expect(shortTerm.data?.length).toBe(2);
      expect(longTerm.data?.length).toBe(10);
    });
  });

  describe('addPortfolioData', () => {
    it('adds portfolio data successfully', async () => {
      const portfolioData: PortfolioData = {
        assetType: 'stocks',
        value: 10000,
        description: 'Test stock investment'
      };
      
      const result = await mockApi.addPortfolioData(portfolioData);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(portfolioData);
    });

    it('validates portfolio data', async () => {
      const invalidData = {
        assetType: 'invalid_type',
        value: -1000,
        description: 'Invalid data'
      } as any;
      
      const result = await mockApi.addPortfolioData(invalidData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('Invalid asset type');
    });

    it('handles missing required fields', async () => {
      const incompleteData = {
        assetType: 'stocks'
        // Missing value
      } as any;
      
      const result = await mockApi.addPortfolioData(incompleteData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('Value is required');
    });

    it('handles negative values', async () => {
      const negativeData: PortfolioData = {
        assetType: 'stocks',
        value: -5000,
        description: 'Negative investment'
      };
      
      const result = await mockApi.addPortfolioData(negativeData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('Value must be positive');
    });
  });

  describe('addPayslipData', () => {
    it('adds payslip data successfully', async () => {
      const payslipData: PayslipData = {
        grossSalary: 8000,
        netSalary: 6000,
        taxDeductions: 1500,
        benefits: 1000,
        payPeriod: 'monthly' as const
      };
      
      const result = await mockApi.addPayslipData(payslipData);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(payslipData);
    });

    it('validates payslip data', async () => {
      const invalidData = {
        grossSalary: 6000,
        netSalary: 8000, // Net salary higher than gross
        benefits: 1000
      } as PayslipData;
      
      const result = await mockApi.addPayslipData(invalidData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('Net salary cannot be higher than gross salary');
    });

    it('handles missing required fields', async () => {
      const incompleteData = {
        grossSalary: 8000
        // Missing netSalary and benefits
      } as any;
      
      const result = await mockApi.addPayslipData(incompleteData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('required');
    });

    it('handles negative values', async () => {
      const negativeData: PayslipData = {
        grossSalary: -8000,
        netSalary: -6000,
        taxDeductions: 1500,
        benefits: -1000,
        payPeriod: 'monthly' as const
      };
      
      const result = await mockApi.addPayslipData(negativeData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('must be positive');
    });
  });

  describe('API response consistency', () => {
    it('returns consistent response structure', async () => {
      const responses = [
        await mockApi.getWealthData(),
        await mockApi.getWealthAnalysis(),
        await mockApi.getFinancialProjections(3),
        await mockApi.addPortfolioData({
          assetType: 'stocks',
          value: 1000,
          description: 'Test'
        }),
        await mockApi.addPayslipData({
          grossSalary: 5000,
          netSalary: 4000,
          taxDeductions: 800,
          benefits: 500,
          payPeriod: 'monthly' as const
        })
      ];
      
      responses.forEach(response => {
        expect(response).toHaveProperty('success');
        expect(typeof response.success).toBe('boolean');
        
        if (response.success) {
          expect(response).toHaveProperty('data');
        } else {
          expect(response).toHaveProperty('error');
          expect(response).toHaveProperty('message');
        }
      });
    });

    it('handles concurrent requests properly', async () => {
      const promises = [
        mockApi.getWealthData(),
        mockApi.getWealthAnalysis(),
        mockApi.getFinancialProjections(2),
        mockApi.addPortfolioData({
          assetType: 'bonds',
          value: 2000,
          description: 'Concurrent test'
        })
      ];
      
      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
      });
    });
  });

  describe('error handling', () => {
    it('handles network-like errors gracefully', async () => {
      // This test simulates what would happen with network errors
      // In a real implementation, we might have network timeout simulation
      const result = await mockApi.getWealthData();
      
      // Mock API should still work
      expect(result.success).toBe(true);
    });

    it('provides meaningful error messages', async () => {
      const invalidPortfolio = {
        assetType: 'invalid',
        value: 'not_a_number'
      } as any;
      
      const result = await mockApi.addPortfolioData(invalidPortfolio);
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
      expect(result.message?.length).toBeGreaterThan(0);
    });
  });
});