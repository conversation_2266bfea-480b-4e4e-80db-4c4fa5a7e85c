import { apiService } from '../services/apiService';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

// Mock axios.create to return the mocked axios instance
const mockAxiosInstance = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  interceptors: {
    request: { use: jest.fn(), eject: jest.fn() },
    response: { use: jest.fn(), eject: jest.fn() }
  }
};

mockAxios.create = jest.fn(() => mockAxiosInstance as any);

describe('API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateProjections', () => {
    it('should validate and process projections parameters', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            { month: 1, value: 1006250 },
            { month: 2, value: 1012543 }
          ]
        }
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const params = {
        currentValue: 1000000,
        target: 1500000,
        monthlyContribution: 6250,
        returnRate: 0.08,
        years: 5
      };

      const result = await apiService.calculateProjections(params);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/analysis/projections', {
        params: {
          target: params.target,
          contributions: params.monthlyContribution,
          rate: params.returnRate,
          years: params.years
        }
      });
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle API errors gracefully', async () => {
      const errorMessage = 'Network Error';
      mockAxiosInstance.get.mockRejectedValue(new Error(errorMessage));

      const params = {
        monthlyContribution: 6250,
        returnRate: 0.08,
        years: 5
      };

      const result = await apiService.calculateProjections(params);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to calculate projections');
    });

    it('should validate input parameters', async () => {
      const invalidParams = {
        monthlyContribution: -1000, // Invalid negative value
        returnRate: 150, // Invalid high return rate
        years: 5
      };

      const result = await apiService.calculateProjections(invalidParams);
      expect(result.success).toBe(false);
      expect(result.message).toContain('must be at least');
    });
  });

  describe('addPortfolioData', () => {
    it('should add portfolio data successfully', async () => {
      const mockResponse = {
        data: { success: true, message: 'Portfolio data added' }
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const portfolioData = {
        assetType: 'stocks' as const,
        value: 10000,
        description: 'Stock investment'
      };

      const result = await apiService.addPortfolioData(portfolioData);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/portfolio', expect.any(Object));
      expect(result.success).toBe(true);
    });

    it('should validate portfolio data', async () => {
      const invalidData = {
        assetType: 'invalid' as any,
        value: -1000,
        description: ''
      };

      const result = await apiService.addPortfolioData(invalidData);
      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid asset type');
    });
  });

  describe('addPayslipData', () => {
    it('should add payslip data successfully', async () => {
      const mockResponse = {
        data: { success: true, message: 'Payslip data added' }
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const payslipData = {
        grossSalary: 8000,
        netSalary: 6000,
        taxDeductions: 1500,
        benefits: 500,
        payPeriod: 'monthly' as const
      };

      const result = await apiService.addPayslipData(payslipData);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/payslips', expect.any(Object));
      expect(result.success).toBe(true);
    });

    it('should validate payslip data structure', async () => {
      const invalidData = {
        grossSalary: 'invalid' as any,
        netSalary: 6000,
        taxDeductions: 1500,
        benefits: 500,
        payPeriod: 'invalid' as any
      };

      const result = await apiService.addPayslipData(invalidData);
      expect(result.success).toBe(false);
      expect(result.message).toContain('must be a valid number');
    });
  });

  describe('getWealthData', () => {
    it('should fetch wealth data from API', async () => {
      const mockPortfolioResponse = {
        data: { success: true, data: [] }
      };
      const mockPayslipResponse = {
        data: { success: true, data: [] }
      };

      mockAxiosInstance.get
        .mockResolvedValueOnce(mockPortfolioResponse)
        .mockResolvedValueOnce(mockPayslipResponse);

      const result = await apiService.getWealthData();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/portfolio');
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/payslips?count=50');
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network Error'));

      const result = await apiService.getWealthData();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to get wealth data');
    });
  });

  describe('getFinancialProjections', () => {
    it('should fetch projections with validation', async () => {
      const mockResponse = {
        data: { success: true, data: [] }
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiService.getFinancialProjections(5);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/analysis/projections?target=1500000&contributions=6250,5260,4200');
      expect(result.success).toBe(true);
    });

    it('should validate years parameter', async () => {
      const result = await apiService.getFinancialProjections(-5);
      expect(result.success).toBe(false);
      expect(result.message).toContain('must be at least');
    });
  });

  describe('healthCheck', () => {
    it('should perform health check', async () => {
      const mockResponse = {
        data: { status: 'ok' }
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiService.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result.success).toBe(true);
    });
  });

  describe('resetData', () => {
    it('should reset data', async () => {
      const mockResponse = {
        data: { success: true }
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await apiService.resetData();

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/data/reset');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getPayslipData', () => {
    it('should validate count parameter', async () => {
      const result = await apiService.getPayslipData(-5);
      expect(result.success).toBe(false);
      expect(result.message).toContain('must be at least');
    });

    it('should fetch payslip data with valid count', async () => {
      const mockResponse = {
        data: { success: true, data: [] }
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiService.getPayslipData(10);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/payslips?count=10');
      expect(result).toEqual(mockResponse.data);
    });
  });
});