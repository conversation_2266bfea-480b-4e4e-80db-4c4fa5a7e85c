import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import Navigation from '../components/Navigation';

describe('Navigation', () => {
  const mockOnTabChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all navigation tabs', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    expect(screen.getByTestId('nav-calculator')).toBeInTheDocument();
    expect(screen.getByTestId('nav-history')).toBeInTheDocument();
    expect(screen.getByText('Calculator')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
  });

  it('highlights the active tab', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    
    expect(calculatorTab).toHaveClass('active');
    expect(historyTab).not.toHaveClass('active');
  });

  it('switches active tab when different tab is selected', () => {
    const { rerender } = render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    // Initially calculator is active
    expect(screen.getByTestId('nav-calculator')).toHaveClass('active');
    expect(screen.getByTestId('nav-history')).not.toHaveClass('active');
    
    // Re-render with history active
    rerender(<Navigation activeTab="history" onTabChange={mockOnTabChange} />);
    
    expect(screen.getByTestId('nav-calculator')).not.toHaveClass('active');
    expect(screen.getByTestId('nav-history')).toHaveClass('active');
  });

  it('calls onTabChange when tab is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    await user.click(historyTab);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('calls onTabChange when calculator tab is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="history" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    await user.click(calculatorTab);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('calculator');
  });

  it('handles keyboard navigation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    
    // Tabs should be focusable
    expect(calculatorTab).toHaveAttribute('tabIndex', '0');
    expect(historyTab).toHaveAttribute('tabIndex', '0');
  });

  it('supports Enter key for tab activation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: 'Enter', code: 'Enter' });
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('supports Space key for tab activation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: ' ', code: 'Space' });
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('does not activate tab on other keys', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: 'Escape', code: 'Escape' });
    
    expect(mockOnTabChange).not.toHaveBeenCalled();
  });

  it('renders with correct ARIA attributes', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    
    expect(calculatorTab).toHaveAttribute('role', 'tab');
    expect(historyTab).toHaveAttribute('role', 'tab');
    expect(calculatorTab).toHaveAttribute('aria-selected', 'true');
    expect(historyTab).toHaveAttribute('aria-selected', 'false');
  });

  it('applies correct CSS classes for styling', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveClass('navigation');
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    
    expect(calculatorTab).toHaveClass('navTab', 'active');
    expect(historyTab).toHaveClass('navTab');
  });

  it('maintains tab order for accessibility', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const tabs = screen.getAllByRole('tab');
    expect(tabs).toHaveLength(2);
    
    // Verify tab order
    expect(tabs[0]).toHaveTextContent('Calculator');
    expect(tabs[1]).toHaveTextContent('History');
  });

  it('prevents default behavior on Space key to avoid scrolling', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    const event = new KeyboardEvent('keydown', { key: ' ', code: 'Space' });
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
    
    fireEvent(historyTab, event);
    
    expect(preventDefaultSpy).toHaveBeenCalled();
  });

  it('handles multiple rapid clicks correctly', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    
    // Click multiple times rapidly
    await user.click(historyTab);
    await user.click(historyTab);
    await user.click(historyTab);
    
    expect(mockOnTabChange).toHaveBeenCalledTimes(3);
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });
});