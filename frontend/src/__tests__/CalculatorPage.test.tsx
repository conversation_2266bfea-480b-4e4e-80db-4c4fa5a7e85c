import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CalculatorPage from '../components/CalculatorPage';
import * as apiService from '../services/apiService';

// Mock the API service
jest.mock('../services/apiService', () => ({
  apiService: {
    calculateProjections: jest.fn()
  }
}));

const mockApiService = apiService.apiService as jest.Mocked<typeof apiService.apiService>;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('CalculatorPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    // Default successful API response
    mockApiService.calculateProjections.mockResolvedValue({
      success: true,
      data: [
        { month: 1, value: 1010000 },
        { month: 60, value: 2500000 },
        { month: 149, value: 3000000 },
        { month: 180, value: 3500000 }
      ]
    });
  });

  it('should render calculator page with default values', () => {
    render(<CalculatorPage />);
    
    expect(screen.getByTestId('calculator-page')).toBeInTheDocument();
    expect(screen.getByText('🧮 Financial Calculator')).toBeInTheDocument();
    expect(screen.getByText('Dynamic wealth projections with real-time updates')).toBeInTheDocument();
    
    // Check default form values
    expect(screen.getByDisplayValue('1000000')).toBeInTheDocument(); // Current portfolio
    expect(screen.getByDisplayValue('3000000')).toBeInTheDocument(); // Target amount
    expect(screen.getByDisplayValue('3000')).toBeInTheDocument(); // Monthly contribution
    expect(screen.getByDisplayValue('7')).toBeInTheDocument(); // Return rate
  });

  it('should show form validation errors for invalid inputs', async () => {
    const user = userEvent.setup();
    render(<CalculatorPage />);
    
    const currentPortfolioInput = screen.getByTestId('current-portfolio-input');
    
    // Clear the input and try to submit
    await user.clear(currentPortfolioInput);
    await user.type(currentPortfolioInput, '-1000');
    
    await user.click(screen.getByTestId('calculate-button'));
    
    await waitFor(() => {
      expect(screen.getByText('Must be positive')).toBeInTheDocument();
    });
  });

  it('should perform calculation when form is submitted', async () => {
    const user = userEvent.setup();
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('calculate-button'));
    
    await waitFor(() => {
      expect(mockApiService.calculateProjections).toHaveBeenCalledWith({
        target: 3000000,
        monthlyContribution: 3000,
        returnRate: 0.07 / 12, // Annual to monthly
        years: 15
      });
    });
    
    // Should show results
    expect(screen.getByTestId('calculation-results')).toBeInTheDocument();
  });

  it('should show calculation results with correct values', async () => {
    const user = userEvent.setup();
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('calculate-button'));
    
    await waitFor(() => {
      expect(screen.getByText('12.4')).toBeInTheDocument(); // Years to target (149/12)
      expect(screen.getByText('149')).toBeInTheDocument(); // Months to target
    });
  });

  it('should handle API errors gracefully', async () => {
    const user = userEvent.setup();
    mockApiService.calculateProjections.mockResolvedValue({
      success: false,
      data: undefined,
      error: 'API Error'
    });
    
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('calculate-button'));
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
      expect(screen.getByText(/Failed to calculate projections/)).toBeInTheDocument();
    });
  });

  it('should update form values dynamically', async () => {
    const user = userEvent.setup();
    render(<CalculatorPage />);
    
    const currentPortfolioInput = screen.getByTestId('current-portfolio-input');
    
    await user.clear(currentPortfolioInput);
    await user.type(currentPortfolioInput, '2000000');
    
    expect(currentPortfolioInput).toHaveValue(2000000);
  });

  it('should save calculation to history when form is submitted', async () => {
    const user = userEvent.setup();
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('calculate-button'));
    
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'calculationHistory',
        expect.stringContaining('1000000')
      );
    });
  });

  it('should load calculation history from localStorage', () => {
    const mockHistory = JSON.stringify([
      {
        id: '1',
        timestamp: new Date().toISOString(),
        inputs: {
          currentPortfolio: 500000,
          targetAmount: 1000000,
          monthlyContribution: 2000,
          annualReturnRate: 8
        },
        results: {
          monthsToTarget: 120,
          yearsToTarget: 10,
          totalContributions: 240000,
          totalGrowth: 260000,
          finalAmount: 1000000,
          contributionShare: 0.48,
          growthShare: 0.52
        }
      }
    ]);
    
    localStorageMock.getItem.mockReturnValue(mockHistory);
    
    render(<CalculatorPage />);
    
    expect(screen.getByTestId('calculation-history')).toBeInTheDocument();
    expect(screen.getByText(/500,000/)).toBeInTheDocument();
    expect(screen.getByText(/1,000,000/)).toBeInTheDocument();
  });

  it('should clear calculation history when clear button is clicked', async () => {
    const user = userEvent.setup();
    const mockHistory = JSON.stringify([
      {
        id: '1',
        timestamp: new Date().toISOString(),
        inputs: { currentPortfolio: 500000, targetAmount: 1000000, monthlyContribution: 2000, annualReturnRate: 8 },
        results: { monthsToTarget: 120, yearsToTarget: 10, totalContributions: 240000, totalGrowth: 260000, finalAmount: 1000000, contributionShare: 0.48, growthShare: 0.52 }
      }
    ]);
    
    localStorageMock.getItem.mockReturnValue(mockHistory);
    
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('clear-history-button'));
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('calculationHistory');
    expect(screen.getByTestId('no-history')).toBeInTheDocument();
  });

  it('should load history item when clicked', async () => {
    const user = userEvent.setup();
    const mockHistory = JSON.stringify([
      {
        id: '1',
        timestamp: new Date().toISOString(),
        inputs: {
          currentPortfolio: 500000,
          targetAmount: 1000000,
          monthlyContribution: 2000,
          annualReturnRate: 8
        },
        results: {
          monthsToTarget: 120,
          yearsToTarget: 10,
          totalContributions: 240000,
          totalGrowth: 260000,
          finalAmount: 1000000,
          contributionShare: 0.48,
          growthShare: 0.52
        }
      }
    ]);
    
    localStorageMock.getItem.mockReturnValue(mockHistory);
    
    render(<CalculatorPage />);
    
    await user.click(screen.getByTestId('history-item-1'));
    
    // Form should be populated with history values
    expect(screen.getByDisplayValue('500000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1000000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('8')).toBeInTheDocument();
  });

  it('should show no results message initially', () => {
    render(<CalculatorPage />);
    
    expect(screen.getByTestId('no-results')).toBeInTheDocument();
    expect(screen.getByText('Enter your financial scenario above to see live calculations')).toBeInTheDocument();
  });

  it('should show no history message when no calculations exist', () => {
    render(<CalculatorPage />);
    
    expect(screen.getByTestId('no-history')).toBeInTheDocument();
    expect(screen.getByText('No calculations yet. Perform a calculation to see it here!')).toBeInTheDocument();
  });

  it('should have proper accessibility attributes', () => {
    render(<CalculatorPage />);
    
    expect(screen.getByLabelText('Current Portfolio ($)')).toBeInTheDocument();
    expect(screen.getByLabelText('Target Amount ($)')).toBeInTheDocument();
    expect(screen.getByLabelText('Monthly Contribution ($)')).toBeInTheDocument();
    expect(screen.getByLabelText('Annual Return Rate (%)')).toBeInTheDocument();
  });

  it('should disable calculate button while calculating', async () => {
    const user = userEvent.setup();
    
    // Make API call take some time
    mockApiService.calculateProjections.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({ success: true, data: [] }), 100))
    );
    
    render(<CalculatorPage />);
    
    const calculateButton = screen.getByTestId('calculate-button');
    
    await user.click(calculateButton);
    
    expect(calculateButton).toBeDisabled();
    
    await waitFor(() => {
      expect(calculateButton).not.toBeDisabled();
    });
  });
});