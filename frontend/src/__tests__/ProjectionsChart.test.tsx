import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProjectionsChart from '../components/ProjectionsChart';
import { FinancialProjection } from '../types';

// Mock D3 functions
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    selectAll: jest.fn(() => ({
      data: jest.fn(() => ({
        enter: jest.fn(() => ({
          append: jest.fn(() => ({
            attr: jest.fn(() => ({
              attr: jest.fn(() => ({
                attr: jest.fn(() => ({
                  attr: jest.fn(() => ({
                    style: jest.fn(() => ({
                      on: jest.fn(() => ({}))
                    }))
                  }))
                }))
              }))
            }))
          }))
        })),
        exit: jest.fn(() => ({
          remove: jest.fn()
        }))
      }))
    })),
    append: jest.fn(() => ({
      attr: jest.fn(() => ({
        attr: jest.fn(() => ({
          attr: jest.fn(() => ({
            attr: jest.fn(() => ({
              style: jest.fn(() => ({
                text: jest.fn(() => ({}))
              }))
            }))
          }))
        }))
      }))
    })),
    node: jest.fn(() => ({
      getBBox: jest.fn(() => ({ width: 100, height: 20 }))
    })),
    style: jest.fn(() => ({})),
    attr: jest.fn(() => ({})),
    text: jest.fn(() => ({}))
  })),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => (value: number) => value * 10)
    }))
  })),
  scaleTime: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => (value: any) => 100)
    }))
  })),
  axisBottom: jest.fn(() => ({
    tickFormat: jest.fn(() => ({}))
  })),
  axisLeft: jest.fn(() => ({
    tickFormat: jest.fn(() => ({}))
  })),
  line: jest.fn(() => ({
    x: jest.fn(() => ({
      y: jest.fn(() => (data: any[]) => 'M10,10L20,20')
    }))
  })),
  extent: jest.fn(() => [0, 100]),
  max: jest.fn(() => 100),
  format: jest.fn(() => (value: number) => `$${value.toLocaleString()}`)
}));

// Mock TooltipInfo component
jest.mock('../components/TooltipInfo', () => {
  return function MockTooltipInfo({ children, content, title }: { children: React.ReactNode; content: string; title?: string }) {
    return (
      <div data-testid="tooltip-info" data-content={content} data-title={title}>
        {children}
      </div>
    );
  };
});

describe('ProjectionsChart', () => {
  const mockProjections: FinancialProjection[] = [
    {
      year: 1,
      projectedWealth: 550000,
      projectedIncome: 98000,
      projectedSavings: 80000,
      projectedExpenses: 18000
    },
    {
      year: 2,
      projectedWealth: 605000,
      projectedIncome: 100000,
      projectedSavings: 85000,
      projectedExpenses: 15000
    },
    {
      year: 3,
      projectedWealth: 665000,
      projectedIncome: 103000,
      projectedSavings: 90000,
      projectedExpenses: 13000
    },
    {
      year: 4,
      projectedWealth: 730000,
      projectedIncome: 106000,
      projectedSavings: 95000,
      projectedExpenses: 11000
    },
    {
      year: 5,
      projectedWealth: 800000,
      projectedIncome: 109000,
      projectedSavings: 100000,
      projectedExpenses: 9000
    }
  ];

  beforeEach(() => {
    // Clear any previous DOM manipulations
    document.body.innerHTML = '';
  });

  it('renders chart container', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });

  it('renders chart title', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    expect(screen.getByText('Financial Projections')).toBeInTheDocument();
  });

  it('renders metric toggle buttons', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    expect(screen.getByText('Wealth')).toBeInTheDocument();
    expect(screen.getByText('Income')).toBeInTheDocument();
    expect(screen.getByText('Savings')).toBeInTheDocument();
  });

  it('has wealth as default selected metric', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const wealthButton = screen.getByText('Wealth');
    expect(wealthButton).toHaveClass('active');
  });

  it('switches metric when buttons are clicked', async () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const incomeButton = screen.getByText('Income');
    fireEvent.click(incomeButton);
    
    await waitFor(() => {
      expect(incomeButton).toHaveClass('active');
    });
    
    const savingsButton = screen.getByText('Savings');
    fireEvent.click(savingsButton);
    
    await waitFor(() => {
      expect(savingsButton).toHaveClass('active');
    });
  });

  it('renders SVG chart element', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const svgElement = screen.getByTestId('chart-svg');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement.tagName).toBe('svg');
  });

  it('handles empty projections gracefully', () => {
    render(<ProjectionsChart projections={[]} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
    expect(screen.getByText('No projection data available')).toBeInTheDocument();
  });

  it('handles single projection data point', () => {
    const singleProjection = [mockProjections[0]];
    
    render(<ProjectionsChart projections={singleProjection} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
    expect(screen.getByText('Wealth')).toBeInTheDocument();
  });

  it('displays chart legend', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    expect(screen.getByText('Current Selection: Wealth')).toBeInTheDocument();
  });

  it('updates legend when metric changes', async () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const incomeButton = screen.getByText('Income');
    fireEvent.click(incomeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Current Selection: Income')).toBeInTheDocument();
    });
  });

  it('handles chart resize', () => {
    const { rerender } = render(<ProjectionsChart projections={mockProjections} />);
    
    // Simulate window resize
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 800,
    });
    
    rerender(<ProjectionsChart projections={mockProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });

  it('provides interactive hover functionality', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const chartContainer = screen.getByTestId('projections-chart');
    
    // Simulate hover events
    fireEvent.mouseEnter(chartContainer);
    fireEvent.mouseMove(chartContainer, { clientX: 100, clientY: 100 });
    fireEvent.mouseLeave(chartContainer);
    
    // Chart should still be rendered
    expect(chartContainer).toBeInTheDocument();
  });

  it('displays correct chart dimensions', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const svgElement = screen.getByTestId('chart-svg');
    expect(svgElement).toHaveAttribute('width');
    expect(svgElement).toHaveAttribute('height');
  });

  it('handles projection data with different scales', () => {
    const extremeProjections: FinancialProjection[] = [
      {
        year: 1,
        projectedWealth: 1000000,
        projectedIncome: 50000,
        projectedSavings: 10000,
        projectedExpenses: 40000
      },
      {
        year: 2,
        projectedWealth: 10000000,
        projectedIncome: 500000,
        projectedSavings: 100000,
        projectedExpenses: 400000
      }
    ];
    
    render(<ProjectionsChart projections={extremeProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
    expect(screen.getByText('Wealth')).toBeInTheDocument();
  });

  it('provides tooltips for chart elements', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const tooltips = screen.getAllByTestId('tooltip-info');
    expect(tooltips.length).toBeGreaterThan(0);
  });

  it('handles negative values in projections', () => {
    const negativeProjections: FinancialProjection[] = [
      {
        year: 1,
        projectedWealth: -100000,
        projectedIncome: 50000,
        projectedSavings: -10000,
        projectedExpenses: 60000
      },
      {
        year: 2,
        projectedWealth: -50000,
        projectedIncome: 55000,
        projectedSavings: -5000,
        projectedExpenses: 60000
      }
    ];
    
    render(<ProjectionsChart projections={negativeProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });

  it('maintains chart state during metric switches', async () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const wealthButton = screen.getByText('Wealth');
    const incomeButton = screen.getByText('Income');
    
    // Switch to income
    fireEvent.click(incomeButton);
    await waitFor(() => {
      expect(incomeButton).toHaveClass('active');
    });
    
    // Switch back to wealth
    fireEvent.click(wealthButton);
    await waitFor(() => {
      expect(wealthButton).toHaveClass('active');
    });
  });

  it('handles rapid metric switching', async () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const buttons = [
      screen.getByText('Wealth'),
      screen.getByText('Income'),
      screen.getByText('Savings')
    ];
    
    // Rapidly switch between metrics
    for (let i = 0; i < 5; i++) {
      const button = buttons[i % 3];
      fireEvent.click(button);
      await waitFor(() => {
        expect(button).toHaveClass('active');
      });
    }
  });

  it('formats large numbers appropriately', () => {
    const largeProjections: FinancialProjection[] = [
      {
        year: 1,
        projectedWealth: 1500000,
        projectedIncome: 250000,
        projectedSavings: 100000,
        projectedExpenses: 150000
      }
    ];
    
    render(<ProjectionsChart projections={largeProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });

  it('handles chart click events', () => {
    const onChartClick = jest.fn();
    
    render(<ProjectionsChart projections={mockProjections} onChartClick={onChartClick} />);
    
    const chartContainer = screen.getByTestId('projections-chart');
    fireEvent.click(chartContainer);
    
    expect(onChartClick).toHaveBeenCalled();
  });

  it('provides accessibility attributes', () => {
    render(<ProjectionsChart projections={mockProjections} />);
    
    const chartContainer = screen.getByTestId('projections-chart');
    expect(chartContainer).toHaveAttribute('role', 'img');
    expect(chartContainer).toHaveAttribute('aria-label');
  });

  it('handles chart data updates', () => {
    const { rerender } = render(<ProjectionsChart projections={mockProjections} />);
    
    const updatedProjections = [
      ...mockProjections,
      {
        year: 6,
        projectedWealth: 900000,
        projectedIncome: 115000,
        projectedSavings: 110000,
        projectedExpenses: 5000
      }
    ];
    
    rerender(<ProjectionsChart projections={updatedProjections} />);
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });

  it('handles chart cleanup on unmount', () => {
    const { unmount } = render(<ProjectionsChart projections={mockProjections} />);
    
    unmount();
    
    // Component should unmount cleanly without errors
    expect(screen.queryByTestId('projections-chart')).not.toBeInTheDocument();
  });
});