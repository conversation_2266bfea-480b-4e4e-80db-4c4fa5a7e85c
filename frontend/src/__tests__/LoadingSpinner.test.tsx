import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import LoadingSpinner from '../components/LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders loading spinner with default message', () => {
    render(<LoadingSpinner />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    const customMessage = 'Analyzing your wealth...';
    render(<LoadingSpinner message={customMessage} />);
    
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  it('applies glass styling', () => {
    render(<LoadingSpinner />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toBeInTheDocument();
  });

  it('shows spinner animation', () => {
    render(<LoadingSpinner />);
    
    const spinnerElement = screen.getByTestId('spinner-animation');
    expect(spinnerElement).toBeInTheDocument();
    // CSS modules transform class names, so we just verify the element exists
    expect(spinnerElement).toHaveAttribute('data-testid', 'spinner-animation');
  });
});