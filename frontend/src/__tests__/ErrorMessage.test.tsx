import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ErrorMessage from '../components/ErrorMessage';

describe('ErrorMessage', () => {
  it('renders error message', () => {
    const errorText = 'Something went wrong!';
    render(<ErrorMessage message={errorText} onDismiss={() => {}} />);
    
    expect(screen.getByText(errorText)).toBeInTheDocument();
    expect(screen.getByTestId('error-message')).toBeInTheDocument();
  });

  it('calls onDismiss when close button is clicked', () => {
    const mockOnDismiss = jest.fn();
    render(<ErrorMessage message="Error" onDismiss={mockOnDismiss} />);
    
    const closeButton = screen.getByTestId('close-button');
    fireEvent.click(closeButton);
    
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it('shows error icon', () => {
    render(<ErrorMessage message="Error" onDismiss={() => {}} />);
    
    expect(screen.getByTestId('error-icon')).toBeInTheDocument();
  });

  it('applies error styling with glass effect', () => {
    render(<ErrorMessage message="Error" onDismiss={() => {}} />);
    
    const errorContainer = screen.getByTestId('error-message');
    expect(errorContainer).toBeInTheDocument();
  });

  it('auto-dismisses after timeout when enabled', async () => {
    const mockOnDismiss = jest.fn();
    render(
      <ErrorMessage 
        message="Error" 
        onDismiss={mockOnDismiss} 
        autoDismiss={true}
        autoDismissTimeout={1000}
      />
    );
    
    // Wait for auto-dismiss
    await new Promise(resolve => setTimeout(resolve, 1100));
    
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });
});