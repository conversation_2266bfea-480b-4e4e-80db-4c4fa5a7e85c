import { renderHook, act, waitFor } from '@testing-library/react';
import { useWealthTracker } from '../hooks/useWealthTracker';
import { mockApi } from '../services/mockApi';
import { PortfolioData, PayslipData, WealthAnalysis, FinancialProjection } from '../types';

// Mock the mockApi
jest.mock('../services/mockApi');
const mockMockApi = mockApi as jest.Mocked<typeof mockApi>;

describe('useWealthTracker', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockMockApi.getWealthData.mockResolvedValue({
      success: true,
      data: {
        portfolioData: [],
        payslipData: []
      }
    });
    
    mockMockApi.getWealthAnalysis.mockResolvedValue({
      success: true,
      data: {
        totalAssets: 500000,
        netWorth: 450000,
        monthlyIncome: 8000,
        annualIncome: 96000,
        savingsRate: 0.25,
        assetBreakdown: {
          stocks: 300000,
          bonds: 100000,
          crypto: 50000,
          savings: 50000
        }
      }
    });
    
    mockMockApi.getFinancialProjections.mockResolvedValue({
      success: true,
      data: [
        {
          year: 1,
          projectedWealth: 550000,
          projectedIncome: 98000,
          projectedSavings: 80000,
          projectedExpenses: 18000
        }
      ]
    });
  });

  it('initializes with default values', () => {
    const { result } = renderHook(() => useWealthTracker());
    
    expect(result.current.wealthData).toBeNull();
    expect(result.current.analysis).toBeNull();
    expect(result.current.projections).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isAddingPortfolio).toBe(false);
    expect(result.current.isAddingPayslip).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('loads data on mount', async () => {
    const { result } = renderHook(() => useWealthTracker());
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    expect(mockMockApi.getWealthData).toHaveBeenCalledTimes(1);
    expect(mockMockApi.getWealthAnalysis).toHaveBeenCalledTimes(1);
    expect(mockMockApi.getFinancialProjections).toHaveBeenCalledTimes(1);
  });

  it('handles loading state correctly', async () => {
    // Make the API call slow to test loading state
    mockMockApi.getWealthData.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({
        success: true,
        data: { portfolioData: [], payslipData: [] }
      }), 100))
    );
    
    const { result } = renderHook(() => useWealthTracker());
    
    expect(result.current.isLoading).toBe(true);
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('handles API errors gracefully', async () => {
    mockMockApi.getWealthData.mockResolvedValue({
      success: false,
      error: 'API Error',
      message: 'Failed to fetch data'
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await waitFor(() => {
      expect(result.current.error).toBe('Failed to fetch data');
    });
  });

  it('adds portfolio data successfully', async () => {
    const portfolioData: PortfolioData = {
      assetType: 'stocks',
      value: 10000,
      description: 'Test stock'
    };
    
    mockMockApi.addPortfolioData.mockResolvedValue({
      success: true,
      data: portfolioData
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await act(async () => {
      const success = await result.current.addPortfolioData(portfolioData);
      expect(success).toBe(true);
    });
    
    expect(mockMockApi.addPortfolioData).toHaveBeenCalledWith(portfolioData);
  });

  it('handles portfolio data addition failure', async () => {
    const portfolioData: PortfolioData = {
      assetType: 'stocks',
      value: 10000,
      description: 'Test stock'
    };
    
    mockMockApi.addPortfolioData.mockResolvedValue({
      success: false,
      error: 'Validation Error',
      message: 'Invalid data'
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await act(async () => {
      const success = await result.current.addPortfolioData(portfolioData);
      expect(success).toBe(false);
    });
    
    expect(result.current.error).toBe('Invalid data');
  });

  it('adds payslip data successfully', async () => {
    const payslipData: PayslipData = {
      grossSalary: 8000,
      netSalary: 6000,
      taxDeductions: 1000,
      benefits: 1000,
      payPeriod: 'monthly' as const
    };
    
    mockMockApi.addPayslipData.mockResolvedValue({
      success: true,
      data: payslipData
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await act(async () => {
      const success = await result.current.addPayslipData(payslipData);
      expect(success).toBe(true);
    });
    
    expect(mockMockApi.addPayslipData).toHaveBeenCalledWith(payslipData);
  });

  it('handles payslip data addition failure', async () => {
    const payslipData: PayslipData = {
      grossSalary: 8000,
      netSalary: 6000,
      taxDeductions: 1000,
      benefits: 1000,
      payPeriod: 'monthly' as const
    };
    
    mockMockApi.addPayslipData.mockResolvedValue({
      success: false,
      error: 'Validation Error',
      message: 'Invalid payslip data'
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await act(async () => {
      const success = await result.current.addPayslipData(payslipData);
      expect(success).toBe(false);
    });
    
    expect(result.current.error).toBe('Invalid payslip data');
  });

  it('refreshes data correctly', async () => {
    const { result } = renderHook(() => useWealthTracker());
    
    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    // Clear mocks to test refresh
    jest.clearAllMocks();
    
    await act(async () => {
      await result.current.loadWealthData();
    });
    
    expect(mockMockApi.getWealthData).toHaveBeenCalledTimes(1);
    expect(mockMockApi.getWealthAnalysis).toHaveBeenCalledTimes(1);
    expect(mockMockApi.getFinancialProjections).toHaveBeenCalledTimes(1);
  });

  it('clears error correctly', async () => {
    mockMockApi.getWealthData.mockResolvedValue({
      success: false,
      error: 'Test Error',
      message: 'Test error message'
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await waitFor(() => {
      expect(result.current.error).toBe('Test error message');
    });
    
    act(() => {
      result.current.clearError();
    });
    
    expect(result.current.error).toBeNull();
  });

  it('handles multiple concurrent operations', async () => {
    const portfolioData: PortfolioData = {
      assetType: 'stocks',
      value: 10000,
      description: 'Test stock'
    };
    
    const payslipData: PayslipData = {
      grossSalary: 8000,
      netSalary: 6000,
      taxDeductions: 1000,
      benefits: 1000,
      payPeriod: 'monthly' as const
    };
    
    mockMockApi.addPortfolioData.mockResolvedValue({
      success: true,
      data: portfolioData
    });
    
    mockMockApi.addPayslipData.mockResolvedValue({
      success: true,
      data: payslipData
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await act(async () => {
      const [portfolioSuccess, payslipSuccess] = await Promise.all([
        result.current.addPortfolioData(portfolioData),
        result.current.addPayslipData(payslipData)
      ]);
      
      expect(portfolioSuccess).toBe(true);
      expect(payslipSuccess).toBe(true);
    });
  });

  it('handles network timeouts', async () => {
    mockMockApi.getWealthData.mockRejectedValue(new Error('Network timeout'));
    
    const { result } = renderHook(() => useWealthTracker());
    
    await waitFor(() => {
      expect(result.current.error).toBe('Network timeout');
    });
  });

  it('maintains loading states during operations', async () => {
    const portfolioData: PortfolioData = {
      assetType: 'stocks',
      value: 10000,
      description: 'Test stock'
    };
    
    let resolvePromise: (value: any) => void;
    const slowPromise = new Promise(resolve => {
      resolvePromise = resolve;
    });
    
    mockMockApi.addPortfolioData.mockReturnValue(slowPromise as Promise<any>);
    
    const { result } = renderHook(() => useWealthTracker());
    
    // Start the operation
    act(() => {
      result.current.addPortfolioData(portfolioData);
    });
    
    // Check loading state
    expect(result.current.isAddingPortfolio).toBe(true);
    
    // Resolve the promise
    await act(async () => {
      resolvePromise!({ success: true, data: portfolioData });
      await slowPromise;
    });
    
    expect(result.current.isAddingPortfolio).toBe(false);
  });

  it('handles empty API responses', async () => {
    mockMockApi.getWealthData.mockResolvedValue({
      success: true,
      data: {
        portfolioData: [],
        payslipData: []
      }
    });
    
    mockMockApi.getWealthAnalysis.mockResolvedValue({
      success: true,
      data: null as any
    });
    
    const { result } = renderHook(() => useWealthTracker());
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    expect(result.current.wealthData).toEqual({
      portfolioData: [],
      payslipData: []
    });
    expect(result.current.analysis).toBeNull();
  });
});