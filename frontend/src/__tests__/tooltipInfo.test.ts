import { getTooltipInfo, tooltipInfo } from '../data/tooltipInfo';

describe('tooltipInfo', () => {
  it('contains all required tooltip information', () => {
    expect(tooltipInfo).toBeDefined();
    expect(typeof tooltipInfo).toBe('object');
  });

  it('has tooltip info for totalAssets', () => {
    const info = tooltipInfo.totalAssets;
    expect(info).toBeDefined();
    expect(info.title).toBe('Total Assets');
    expect(info.content).toContain('Sum of all your investments');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for netWorth', () => {
    const info = tooltipInfo.netWorth;
    expect(info).toBeDefined();
    expect(info.title).toBe('Net Worth');
    expect(info.content).toContain('total assets minus estimated liabilities');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for monthlyIncome', () => {
    const info = tooltipInfo.monthlyIncome;
    expect(info).toBeDefined();
    expect(info.title).toBe('Monthly Income');
    expect(info.content).toContain('monthly take-home pay');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for annualIncome', () => {
    const info = tooltipInfo.annualIncome;
    expect(info).toBeDefined();
    expect(info.title).toBe('Annual Income');
    expect(info.content).toContain('total yearly take-home pay');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for savingsRate', () => {
    const info = tooltipInfo.savingsRate;
    expect(info).toBeDefined();
    expect(info.title).toBe('Savings Rate');
    expect(info.content).toContain('Percentage of your income');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for stocks', () => {
    const info = tooltipInfo.stocks;
    expect(info).toBeDefined();
    expect(info.title).toBe('Stocks Portfolio');
    expect(info.content).toContain('equity investments');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for bonds', () => {
    const info = tooltipInfo.bonds;
    expect(info).toBeDefined();
    expect(info.title).toBe('Bonds & Fixed Income');
    expect(info.content).toContain('fixed-income securities');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for crypto', () => {
    const info = tooltipInfo.crypto;
    expect(info).toBeDefined();
    expect(info.title).toBe('Cryptocurrency');
    expect(info.content).toContain('digital assets');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for savings', () => {
    const info = tooltipInfo.savings;
    expect(info).toBeDefined();
    expect(info.title).toBe('Cash & Savings');
    expect(info.content).toContain('cash equivalents');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for assetType', () => {
    const info = tooltipInfo.assetType;
    expect(info).toBeDefined();
    expect(info.title).toBe('Asset Type');
    expect(info.content).toContain('category of your investment');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for assetValue', () => {
    const info = tooltipInfo.assetValue;
    expect(info).toBeDefined();
    expect(info.title).toBe('Asset Value');
    expect(info.content).toContain('current market value');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for grossSalary', () => {
    const info = tooltipInfo.grossSalary;
    expect(info).toBeDefined();
    expect(info.title).toBe('Gross Salary');
    expect(info.content).toContain('before any deductions');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for netSalary', () => {
    const info = tooltipInfo.netSalary;
    expect(info).toBeDefined();
    expect(info.title).toBe('Net Salary / Take-Home Pay');
    expect(info.content).toContain('after all deductions');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for benefits', () => {
    const info = tooltipInfo.benefits;
    expect(info).toBeDefined();
    expect(info.title).toBe('Benefits Value');
    expect(info.content).toContain('employer-provided benefits');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for projectedWealth', () => {
    const info = tooltipInfo.projectedWealth;
    expect(info).toBeDefined();
    expect(info.title).toBe('Projected Wealth');
    expect(info.content).toContain('Estimated future value');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for projectedIncome', () => {
    const info = tooltipInfo.projectedIncome;
    expect(info).toBeDefined();
    expect(info.title).toBe('Projected Income');
    expect(info.content).toContain('Estimated future income');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for projectedSavings', () => {
    const info = tooltipInfo.projectedSavings;
    expect(info).toBeDefined();
    expect(info.title).toBe('Projected Savings');
    expect(info.content).toContain('Estimated annual savings');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for investmentGrowth', () => {
    const info = tooltipInfo.investmentGrowth;
    expect(info).toBeDefined();
    expect(info.title).toBe('Investment Growth');
    expect(info.content).toContain('increase in investment value');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for riskTolerance', () => {
    const info = tooltipInfo.riskTolerance;
    expect(info).toBeDefined();
    expect(info.title).toBe('Risk Tolerance');
    expect(info.content).toContain('ability to handle investment volatility');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for diversification', () => {
    const info = tooltipInfo.diversification;
    expect(info).toBeDefined();
    expect(info.title).toBe('Diversification');
    expect(info.content).toContain('spreading investments across different assets');
    expect(info.content.length).toBeGreaterThan(50);
  });

  it('has tooltip info for compoundInterest', () => {
    const info = tooltipInfo.compoundInterest;
    expect(info).toBeDefined();
    expect(info.title).toBe('Compound Interest');
    expect(info.content).toContain('Interest earned on both principal and previous interest');
    expect(info.content.length).toBeGreaterThan(50);
  });
});

describe('getTooltipInfo function', () => {
  it('returns correct tooltip info for valid keys', () => {
    const totalAssetsInfo = getTooltipInfo('totalAssets');
    expect(totalAssetsInfo.title).toBe('Total Assets');
    expect(totalAssetsInfo.content).toContain('Sum of all your investments');
  });

  it('returns default tooltip info for invalid keys', () => {
    const invalidInfo = getTooltipInfo('nonExistentKey' as any);
    expect(invalidInfo.title).toBe('Information');
    expect(invalidInfo.content).toBe('No detailed information available for this field.');
  });

  it('handles null and undefined keys gracefully', () => {
    const nullInfo = getTooltipInfo(null as any);
    expect(nullInfo.title).toBe('Information');
    expect(nullInfo.content).toBe('No detailed information available for this field.');
    
    const undefinedInfo = getTooltipInfo(undefined as any);
    expect(undefinedInfo.title).toBe('Information');
    expect(undefinedInfo.content).toBe('No detailed information available for this field.');
  });

  it('returns the same object reference for valid keys', () => {
    const info1 = getTooltipInfo('totalAssets');
    const info2 = getTooltipInfo('totalAssets');
    expect(info1).toBe(info2);
  });

  it('works with all defined tooltip keys', () => {
    const keys = Object.keys(tooltipInfo) as Array<keyof typeof tooltipInfo>;
    
    keys.forEach(keyName => {
      const info = getTooltipInfo(keyName.toString());
      expect(info).toBeDefined();
      expect(info.title).toBeDefined();
      expect(info.content).toBeDefined();
      expect(typeof info.title).toBe('string');
      expect(typeof info.content).toBe('string');
      expect(info.title.length).toBeGreaterThan(0);
      expect(info.content.length).toBeGreaterThan(0);
    });
  });

  it('has consistent structure for all tooltip entries', () => {
    const keys = Object.keys(tooltipInfo);
    
    keys.forEach(key => {
      const info = tooltipInfo[key as keyof typeof tooltipInfo];
      expect(info).toHaveProperty('title');
      expect(info).toHaveProperty('content');
      expect(typeof info.title).toBe('string');
      expect(typeof info.content).toBe('string');
    });
  });

  it('contains financial education content', () => {
    const info = tooltipInfo.compoundInterest;
    expect(info.content).toContain('Albert Einstein');
    expect(info.content).toContain('eighth wonder of the world');
  });

  it('provides practical examples in tooltips', () => {
    const savingsInfo = tooltipInfo.savingsRate;
    expect(savingsInfo.content).toContain('15-20%');
    expect(savingsInfo.content).toContain('Higher rates accelerate');
  });

  it('includes risk and strategy information', () => {
    const stocksInfo = tooltipInfo.stocks;
    expect(stocksInfo.content).toContain('higher long-term returns');
    expect(stocksInfo.content).toContain('volatility');
    
    const bondsInfo = tooltipInfo.bonds;
    expect(bondsInfo.content).toContain('stability');
    expect(bondsInfo.content).toContain('regular income');
  });

  it('provides comprehensive explanations', () => {
    const diversificationInfo = tooltipInfo.diversification;
    expect(diversificationInfo.content).toContain("Don't put all your eggs in one basket");
    expect(diversificationInfo.content).toContain('reduce overall portfolio risk');
  });
});