import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import App from '../App';
import * as mockApi from '../services/mockApi';

// Mock the API service
jest.mock('../services/mockApi');
const mockedMockApi = mockApi as jest.Mocked<typeof mockApi>;

// Mock browser APIs
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
});

global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();
HTMLElement.prototype.click = jest.fn();

describe('Frontend-Backend Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockedMockApi.default.getWealthData.mockResolvedValue({
      success: true,
      data: {
        totalAssets: 800000,
        netWorth: 750000,
        monthlyIncome: 8000,
        annualIncome: 96000,
        savingsRate: 0.2,
        assetBreakdown: {
          stocks: 500000,
          bonds: 200000,
          savings: 100000
        }
      }
    });

    mockedMockApi.default.getWealthAnalysis.mockResolvedValue({
      success: true,
      data: {
        totalAssets: 800000,
        netWorth: 750000,
        monthlyIncome: 8000,
        annualIncome: 96000,
        savingsRate: 0.2,
        assetBreakdown: {
          stocks: 500000,
          bonds: 200000,
          savings: 100000
        }
      }
    });

    mockedMockApi.default.getFinancialProjections.mockResolvedValue({
      success: true,
      data: [
        {
          month: 1,
          portfolioValue: 800000,
          monthlyContribution: 5000,
          interestEarned: 5333,
          cumulativeContribution: 5000,
          totalValue: 810333
        },
        {
          month: 2,
          portfolioValue: 810333,
          monthlyContribution: 5000,
          interestEarned: 5402,
          cumulativeContribution: 10000,
          totalValue: 820735
        }
      ]
    });

    mockedMockApi.default.addPortfolioData.mockResolvedValue({
      success: true,
      message: 'Portfolio data added successfully'
    });

    mockedMockApi.default.addPayslipData.mockResolvedValue({
      success: true,
      message: 'Payslip data added successfully'
    });

    // Mock localStorage to return empty initially
    (window.localStorage.getItem as jest.Mock).mockReturnValue(null);
  });

  describe('Full Application Workflow', () => {
    it('should complete end-to-end wealth calculation workflow', async () => {
      const user = userEvent.setup();
      render(<App />);

      // 1. Verify initial load
      await waitFor(() => {
        expect(screen.getByText('💰 Wealth Tracker')).toBeInTheDocument();
      });

      // 2. Navigate to Calculator
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      await waitFor(() => {
        expect(screen.getByText('Financial Calculator')).toBeInTheDocument();
      });

      // 3. Fill out calculator form
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      const targetInput = screen.getByLabelText(/target amount/i);
      const contributionInput = screen.getByLabelText(/monthly contribution/i);
      const returnRateInput = screen.getByLabelText(/annual return rate/i);

      await user.clear(portfolioInput);
      await user.type(portfolioInput, '800000');
      
      await user.clear(targetInput);
      await user.type(targetInput, '1500000');
      
      await user.clear(contributionInput);
      await user.type(contributionInput, '6250');
      
      await user.clear(returnRateInput);
      await user.type(returnRateInput, '7');

      // 4. Submit calculation
      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      // 5. Verify API calls were made
      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalledWith(
          expect.any(Number)
        );
      });

      // 6. Navigate to History and verify data
      const historyTab = screen.getByTestId('nav-history');
      await user.click(historyTab);

      await waitFor(() => {
        expect(screen.getByText('📈 Financial History')).toBeInTheDocument();
      });

      // 7. Verify calculation was saved to history
      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'calculationHistory',
        expect.stringContaining('800000')
      );
    });

    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock API failure
      mockedMockApi.default.getFinancialProjections.mockRejectedValue(
        new Error('API Error')
      );

      render(<App />);

      // Navigate to Calculator
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      // Fill form with valid data
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '800000');

      const targetInput = screen.getByLabelText(/target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, '1500000');

      const contributionInput = screen.getByLabelText(/monthly contribution/i);
      await user.clear(contributionInput);
      await user.type(contributionInput, '6250');

      const returnRateInput = screen.getByLabelText(/annual return rate/i);
      await user.clear(returnRateInput);
      await user.type(returnRateInput, '7');

      // Submit and expect error handling
      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to calculate projections/i)).toBeInTheDocument();
      });
    });

    it('should sync data between calculator and history pages', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate to Calculator and make calculation
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      // Fill form
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '786000');

      const targetInput = screen.getByLabelText(/target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, '1500000');

      const contributionInput = screen.getByLabelText(/monthly contribution/i);
      await user.clear(contributionInput);
      await user.type(contributionInput, '6250');

      // Calculate
      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      // Wait for calculation to complete
      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalled();
      });

      // Navigate to History
      const historyTab = screen.getByTestId('nav-history');
      await user.click(historyTab);

      // Verify the calculation appears in history
      await waitFor(() => {
        expect(screen.getByText(/786,000/)).toBeInTheDocument();
        expect(screen.getByText(/1,500,000/)).toBeInTheDocument();
      });
    });

    it('should handle real-time calculation updates', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate to Calculator
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      // Fill initial values
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '500000');

      const targetInput = screen.getByLabelText(/target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, '1000000');

      const contributionInput = screen.getByLabelText(/monthly contribution/i);
      await user.clear(contributionInput);
      await user.type(contributionInput, '3000');

      const returnRateInput = screen.getByLabelText(/annual return rate/i);
      await user.clear(returnRateInput);
      await user.type(returnRateInput, '7');

      // Wait for debounced calculation
      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalled();
      }, { timeout: 3000 });

      // Change contribution amount
      await user.clear(contributionInput);
      await user.type(contributionInput, '5000');

      // Wait for new calculation
      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalledTimes(2);
      }, { timeout: 3000 });
    });
  });

  describe('Data Persistence Integration', () => {
    it('should persist and restore calculation history', async () => {
      const user = userEvent.setup();
      
      // Mock existing calculation history
      const existingHistory = JSON.stringify([
        {
          id: 'test-1',
          timestamp: new Date().toISOString(),
          inputs: {
            currentPortfolio: 800000,
            targetAmount: 1500000,
            monthlyContribution: 6250,
            annualReturnRate: 7
          },
          results: {
            monthsToTarget: 15,
            yearsToTarget: 1.25,
            totalContributions: 93750,
            totalGrowth: 606250
          }
        }
      ]);

      (window.localStorage.getItem as jest.Mock).mockReturnValue(existingHistory);

      render(<App />);

      // Navigate to History
      const historyTab = screen.getByTestId('nav-history');
      await user.click(historyTab);

      // Verify existing history is displayed
      await waitFor(() => {
        expect(screen.getByText(/800,000/)).toBeInTheDocument();
        expect(screen.getByText(/1,500,000/)).toBeInTheDocument();
        expect(screen.getByText(/15/)).toBeInTheDocument(); // months
      });
    });

    it('should export calculation history', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate to History
      const historyTab = screen.getByTestId('nav-history');
      await user.click(historyTab);

      // Click export button
      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      // Verify export functionality was triggered
      expect(global.URL.createObjectURL).toHaveBeenCalled();
    });

    it('should clear calculation history', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate to History
      const historyTab = screen.getByTestId('nav-history');
      await user.click(historyTab);

      // Click clear button
      const clearButton = screen.getByTestId('clear-button');
      await user.click(clearButton);

      // Verify localStorage clear was called
      expect(window.localStorage.removeItem).toHaveBeenCalledWith('calculationHistory');
    });
  });

  describe('Cross-Component Data Flow', () => {
    it('should update wealth analysis when portfolio data changes', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Start with default wealth analysis
      await waitFor(() => {
        expect(mockedMockApi.default.getWealthAnalysis).toHaveBeenCalled();
      });

      // Navigate to Calculator and make changes
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      // Make a calculation that should trigger wealth analysis update
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '900000');

      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      // Verify API calls reflect the updated data
      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalled();
      });
    });

    it('should maintain state consistency across navigation', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate between tabs
      const calculatorTab = screen.getByTestId('nav-calculator');
      const historyTab = screen.getByTestId('nav-history');

      await user.click(calculatorTab);
      await waitFor(() => {
        expect(screen.getByText('Financial Calculator')).toBeInTheDocument();
      });

      await user.click(historyTab);
      await waitFor(() => {
        expect(screen.getByText('📈 Financial History')).toBeInTheDocument();
      });

      await user.click(calculatorTab);
      await waitFor(() => {
        expect(screen.getByText('Financial Calculator')).toBeInTheDocument();
      });

      // Verify state is maintained (form values, etc.)
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      expect(portfolioInput).toBeInTheDocument();
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary API failures', async () => {
      const user = userEvent.setup();
      
      // First call fails, second succeeds
      mockedMockApi.default.getFinancialProjections
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              month: 1,
              portfolioValue: 800000,
              monthlyContribution: 5000,
              interestEarned: 5333,
              cumulativeContribution: 5000,
              totalValue: 810333
            }
          ]
        });

      render(<App />);

      // Navigate to Calculator
      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      // Fill form
      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '800000');

      // First attempt (should fail)
      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to calculate projections/i)).toBeInTheDocument();
      });

      // Second attempt (should succeed)
      await user.click(calculateButton);

      await waitFor(() => {
        expect(mockedMockApi.default.getFinancialProjections).toHaveBeenCalledTimes(2);
      });
    });

    it('should handle malformed API responses', async () => {
      const user = userEvent.setup();
      
      // Mock malformed response
      mockedMockApi.default.getFinancialProjections.mockResolvedValue({
        success: true,
        data: null as any
      });

      render(<App />);

      const calculatorTab = screen.getByTestId('nav-calculator');
      await user.click(calculatorTab);

      const portfolioInput = screen.getByLabelText(/current portfolio/i);
      await user.clear(portfolioInput);
      await user.type(portfolioInput, '800000');

      const calculateButton = screen.getByText('Calculate Financial Plan');
      await user.click(calculateButton);

      // Should handle gracefully without crashing
      await waitFor(() => {
        expect(screen.getByText(/failed to calculate projections/i)).toBeInTheDocument();
      });
    });
  });
});