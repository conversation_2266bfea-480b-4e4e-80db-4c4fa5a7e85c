import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock the useWealthTracker hook
jest.mock('../hooks/useWealthTracker', () => ({
  __esModule: true,
  default: () => ({
    wealthData: {
      totalAssets: 500000,
      netWorth: 450000,
      monthlyIncome: 8000,
      annualIncome: 96000,
      savingsRate: 0.15,
      assetBreakdown: {
        stocks: 300000,
        bonds: 150000,
        savings: 50000
      }
    },
    analysis: {
      totalAssets: 500000,
      netWorth: 450000,
      monthlyIncome: 8000,
      annualIncome: 96000,
      savingsRate: 0.15,
      assetBreakdown: {
        stocks: 300000,
        bonds: 150000,
        savings: 50000
      }
    },
    historicalData: [],
    isLoading: false,
    error: null,
    loadWealthData: jest.fn(),
    addPortfolioData: jest.fn(),
    addPayslipData: jest.fn(),
    analyzeWealth: jest.fn()
  })
}));

// Mock all the child components
jest.mock('../components/WealthTrackerForm', () => {
  return function MockWealthTrackerForm({ onDataAdded }: { onDataAdded: () => void }) {
    return (
      <div data-testid="wealth-tracker-form">
        <button onClick={onDataAdded}>Mock Submit</button>
      </div>
    );
  };
});

jest.mock('../components/WealthAnalysisDisplay', () => {
  return function MockWealthAnalysisDisplay() {
    return <div data-testid="wealth-analysis-display">Mock Analysis</div>;
  };
});

jest.mock('../components/HistoricalDataTable', () => {
  return function MockHistoricalDataTable() {
    return <div data-testid="historical-data-table">Mock Table</div>;
  };
});

jest.mock('../components/ProjectionsChart', () => {
  return function MockProjectionsChart() {
    return <div data-testid="projections-chart">Mock Chart</div>;
  };
});

jest.mock('../components/LoadingSpinner', () => {
  return function MockLoadingSpinner({ message }: { message?: string }) {
    return <div data-testid="loading-spinner">{message || 'Loading...'}</div>;
  };
});

jest.mock('../components/ErrorMessage', () => {
  return function MockErrorMessage({ message }: { message: string }) {
    return <div data-testid="error-message">{message}</div>;
  };
});

// Mock the useWealthTracker hook
const mockUseWealthTracker = jest.fn();
jest.mock('../hooks/useWealthTracker', () => ({
  useWealthTracker: () => mockUseWealthTracker()
}));

describe('App', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseWealthTracker.mockReturnValue({
      wealthData: null,
      analysis: null,
      isLoading: false,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: null,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: jest.fn(),
      clearError: jest.fn()
    });
  });

  it('renders app title', () => {
    render(<App />);
    expect(screen.getByText('Wealth Tracker')).toBeInTheDocument();
  });

  it('renders tab navigation', () => {
    render(<App />);
    expect(screen.getByText('Data Entry')).toBeInTheDocument();
    expect(screen.getByText('Analysis')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
    expect(screen.getByText('Projections')).toBeInTheDocument();
  });

  it('shows loading state when loading', () => {
    mockUseWealthTracker.mockReturnValue({
      wealthData: null,
      analysis: null,
      isLoading: true,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: null,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: jest.fn(),
      clearError: jest.fn()
    });

    render(<App />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    const errorMessage = 'Test error message';
    mockUseWealthTracker.mockReturnValue({
      wealthData: null,
      analysis: null,
      isLoading: false,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: errorMessage,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: jest.fn(),
      clearError: jest.fn()
    });

    render(<App />);
    expect(screen.getByTestId('error-message')).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('shows data entry form by default', () => {
    render(<App />);
    expect(screen.getByTestId('wealth-tracker-form')).toBeInTheDocument();
  });

  it('calls refreshData when data is added', async () => {
    const mockRefreshData = jest.fn();
    mockUseWealthTracker.mockReturnValue({
      wealthData: null,
      analysis: null,
      isLoading: false,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: null,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: mockRefreshData,
      clearError: jest.fn()
    });

    render(<App />);
    
    const mockSubmitButton = screen.getByText('Mock Submit');
    mockSubmitButton.click();
    
    await waitFor(() => {
      expect(mockRefreshData).toHaveBeenCalledTimes(1);
    });
  });

  it('shows analysis when analysis data is available', () => {
    const mockAnalysis = {
      totalAssets: 500000,
      netWorth: 450000,
      monthlyIncome: 8000,
      annualIncome: 96000,
      savingsRate: 0.25,
      assetBreakdown: {
        stocks: 300000,
        bonds: 100000,
        crypto: 50000,
        savings: 50000
      }
    };

    mockUseWealthTracker.mockReturnValue({
      wealthData: { portfolioData: [], payslipData: [] },
      analysis: mockAnalysis,
      isLoading: false,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: null,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: jest.fn(),
      clearError: jest.fn()
    });

    render(<App />);
    
    // Click analysis tab
    const analysisTab = screen.getByText('Analysis');
    analysisTab.click();
    
    expect(screen.getByTestId('wealth-analysis-display')).toBeInTheDocument();
  });

  it('shows historical data table in history tab', () => {
    const mockWealthData = {
      portfolioData: [
        { assetType: 'stocks' as const, value: 100000, dateAdded: new Date() }
      ],
      payslipData: [
        { grossSalary: 8000, netSalary: 6000, benefits: 1000, dateAdded: new Date() }
      ]
    };

    mockUseWealthTracker.mockReturnValue({
      wealthData: mockWealthData,
      analysis: null,
      isLoading: false,
      isAddingPortfolio: false,
      isAddingPayslip: false,
      error: null,
      addPortfolioData: jest.fn(),
      addPayslipData: jest.fn(),
      refreshData: jest.fn(),
      clearError: jest.fn()
    });

    render(<App />);
    
    // Click history tab
    const historyTab = screen.getByText('History');
    historyTab.click();
    
    expect(screen.getByTestId('historical-data-table')).toBeInTheDocument();
  });

  it('shows projections chart in projections tab', () => {
    render(<App />);
    
    // Click projections tab
    const projectionsTab = screen.getByText('Projections');
    projectionsTab.click();
    
    expect(screen.getByTestId('projections-chart')).toBeInTheDocument();
  });
});