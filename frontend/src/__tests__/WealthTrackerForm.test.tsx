import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import WealthTrackerForm from '../components/WealthTrackerForm';

// Mock the useWealthTracker hook
const mockAddPortfolioData = jest.fn();
const mockAddPayslipData = jest.fn();
const mockUseWealthTracker = jest.fn();

jest.mock('../hooks/useWealthTracker', () => ({
  useWealthTracker: () => mockUseWealthTracker()
}));

describe('WealthTrackerForm', () => {
  const mockOnDataAdded = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations
    mockAddPortfolioData.mockResolvedValue(true);
    mockAddPayslipData.mockResolvedValue(true);
    
    // Default mock return value
    mockUseWealthTracker.mockReturnValue({
      addPortfolioData: mockAddPortfolioData,
      addPayslipData: mockAddPayslipData,
      isAddingPortfolio: false,
      isAddingPayslip: false
    });
  });

  it('renders form with portfolio tab active by default', () => {
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    expect(screen.getByTestId('wealth-tracker-form')).toBeInTheDocument();
    expect(screen.getByTestId('portfolio-form')).toBeInTheDocument();
    expect(screen.queryByTestId('payslip-form')).not.toBeInTheDocument();
  });

  it('switches to payslip tab when clicked', async () => {
    const user = userEvent.setup();
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    await user.click(screen.getByTestId('payslip-tab'));
    
    expect(screen.getByTestId('payslip-form')).toBeInTheDocument();
    expect(screen.queryByTestId('portfolio-form')).not.toBeInTheDocument();
  });

  it('validates required portfolio fields', async () => {
    const user = userEvent.setup();
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    await user.click(screen.getByTestId('submit-button'));
    
    await waitFor(() => {
      expect(screen.getByTestId('asset-type-error')).toBeInTheDocument();
    });
  });

  it('submits portfolio data correctly', async () => {
    const user = userEvent.setup();
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    // Fill out the form
    await user.selectOptions(screen.getByTestId('asset-type-select'), 'stocks');
    await user.type(screen.getByTestId('asset-value-input'), '50000');
    await user.type(screen.getByTestId('asset-description-input'), 'Tech stocks');
    
    await user.click(screen.getByTestId('submit-button'));
    
    await waitFor(() => {
      expect(mockOnDataAdded).toHaveBeenCalledTimes(1);
    });
  });

  it('submits payslip data correctly', async () => {
    const user = userEvent.setup();
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    // Switch to payslip tab
    await user.click(screen.getByTestId('payslip-tab'));
    
    // Fill out payslip form
    await user.type(screen.getByTestId('gross-salary-input'), '8000');
    await user.type(screen.getByTestId('net-salary-input'), '6000');
    await user.type(screen.getByTestId('tax-deductions-input'), '1500');
    await user.type(screen.getByTestId('benefits-input'), '500');
    await user.selectOptions(screen.getByTestId('pay-period-select'), 'monthly');
    
    await user.click(screen.getByTestId('submit-button'));
    
    await waitFor(() => {
      expect(mockOnDataAdded).toHaveBeenCalledTimes(1);
    });
  });

  it('shows loading state during submission', async () => {
    // Mock loading state
    mockUseWealthTracker.mockReturnValue({
      addPortfolioData: mockAddPortfolioData,
      addPayslipData: mockAddPayslipData,
      isAddingPortfolio: true,
      isAddingPayslip: false
    });
    
    render(<WealthTrackerForm onDataAdded={mockOnDataAdded} />);
    
    const submitButton = screen.getByTestId('submit-button');
    expect(submitButton).toBeDisabled();
    expect(submitButton).toHaveTextContent('Adding...');
  });
});