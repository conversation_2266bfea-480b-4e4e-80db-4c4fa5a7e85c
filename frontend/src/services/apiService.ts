import axios from 'axios';
import {
  PortfolioData,
  PayslipData,
  WealthAnalysis,
  FinancialProjection,
  WealthTrackerData,
  ApiResponse
} from '../types';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
const API_TIMEOUT = 10000; // 10 seconds

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Validation helper functions
const validateNumber = (value: any, name: string, min?: number, max?: number): void => {
  if (typeof value !== 'number' || isNaN(value)) {
    throw new Error(`${name} must be a valid number`);
  }
  if (min !== undefined && value < min) {
    throw new Error(`${name} must be at least ${min}`);
  }
  if (max !== undefined && value > max) {
    throw new Error(`${name} must be at most ${max}`);
  }
};

const validatePortfolioData = (data: any): void => {
  if (!data || typeof data !== 'object') {
    throw new Error('Portfolio data is required');
  }
  if (!data.assetType || typeof data.assetType !== 'string') {
    throw new Error('Asset type is required');
  }
  const validAssetTypes = ['stocks', 'bonds', 'crypto', 'real_estate', 'savings', 'other'];
  if (!validAssetTypes.includes(data.assetType)) {
    throw new Error('Invalid asset type');
  }
  validateNumber(data.value, 'Value', 0);
};

const validatePayslipData = (data: any): void => {
  if (!data || typeof data !== 'object') {
    throw new Error('Payslip data is required');
  }
  validateNumber(data.grossSalary, 'Gross salary', 0);
  validateNumber(data.netSalary, 'Net salary', 0);
  validateNumber(data.taxDeductions, 'Tax deductions', 0);
  validateNumber(data.benefits, 'Benefits', 0);
  
  if (!data.payPeriod || !['monthly', 'bi-weekly', 'weekly'].includes(data.payPeriod)) {
    throw new Error('Invalid pay period');
  }
};

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    if (error.response?.status === 429) {
      throw new Error('Too many requests. Please try again later.');
    }
    if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. Please check your connection.');
    }
    throw error;
  }
);

// Real API service that connects to backend
export const apiService = {
  // Health check
  healthCheck: async (): Promise<ApiResponse<any>> => {
    try {
      const response = await api.get('/health');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Configuration endpoints
  getConfig: async (): Promise<ApiResponse<any>> => {
    try {
      const response = await api.get('/api/config');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get configuration',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  updateConfig: async (config: any): Promise<ApiResponse<void>> => {
    try {
      const response = await api.put('/api/config', config);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update configuration',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  setWealthTargets: async (targets: number[]): Promise<ApiResponse<void>> => {
    try {
      const response = await api.put('/api/config/targets', { targets });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update wealth targets',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  setDefaultContributions: async (contributions: number[]): Promise<ApiResponse<void>> => {
    try {
      const response = await api.put('/api/config/contributions', { contributions });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update contributions',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  setDefaultTarget: async (target: number): Promise<ApiResponse<void>> => {
    try {
      const response = await api.put('/api/config/target', { target });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update target',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Portfolio endpoints
  addPortfolioData: async (data: PortfolioData): Promise<ApiResponse<PortfolioData>> => {
    try {
      // Convert frontend PortfolioData to backend format
      const backendData = {
        year: new Date().getFullYear(), // Current year
        month: new Date().getMonth() + 1, // Current month
        trow: data.assetType === 'stocks' ? data.value : 0,
        robinhood: data.assetType === 'crypto' ? data.value : 0,
        etrade: data.assetType === 'bonds' ? data.value : 0,
        teradata: data.assetType === 'real_estate' ? data.value : 0,
        fidelity: data.assetType === 'savings' ? data.value : 0
      };
      
      const response = await api.post('/api/portfolio', backendData);
      return {
        success: response.data.success,
        data,
        message: response.data.message
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to add portfolio data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  getPortfolioData: async (): Promise<ApiResponse<any[]>> => {
    try {
      const response = await api.get('/api/portfolio');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get portfolio data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Payslip endpoints
  addPayslipData: async (data: PayslipData): Promise<ApiResponse<PayslipData>> => {
    try {
      const backendData = {
        date: new Date().toISOString().split('T')[0], // Current date
        gross: data.grossSalary,
        net: data.netSalary,
        espp: 0, // Default values - could be mapped from frontend
        rothE: data.benefits / 2, // Split benefits
        rothR: data.benefits / 2
      };
      
      const response = await api.post('/api/payslips', backendData);
      return {
        success: response.data.success,
        data,
        message: response.data.message
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to add payslip data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  getPayslipData: async (count: number = 10): Promise<ApiResponse<any[]>> => {
    try {
      const response = await api.get(`/api/payslips?count=${count}`);
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get payslip data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Analysis endpoints
  getWealthAnalysis: async (): Promise<ApiResponse<WealthAnalysis>> => {
    try {
      const response = await api.get('/api/analysis/basic');
      if (response.data.success) {
        // Convert backend analysis to frontend format
        const backendData = response.data.data;
        const frontendAnalysis: WealthAnalysis = {
          totalAssets: backendData.currentPortfolioValue,
          netWorth: backendData.currentPortfolioValue * 0.9, // Estimated
          monthlyIncome: backendData.monthlyContribution,
          annualIncome: backendData.monthlyContribution * 12,
          savingsRate: 0.2, // Estimated
          assetBreakdown: {
            stocks: backendData.currentPortfolioValue * 0.6,
            bonds: backendData.currentPortfolioValue * 0.2,
            crypto: backendData.currentPortfolioValue * 0.1,
            savings: backendData.currentPortfolioValue * 0.1
          }
        };
        return {
          success: true,
          data: frontendAnalysis
        };
      }
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get wealth analysis',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  getFinancialProjections: async (years: number): Promise<ApiResponse<FinancialProjection[]>> => {
    try {
      const response = await api.get(`/api/analysis/projections?target=1500000&contributions=6250,5260,4200`);
      if (response.data.success) {
        // Convert backend projections to frontend format
        const projections: FinancialProjection[] = [];
        for (let year = 1; year <= years; year++) {
          projections.push({
            year,
            projectedWealth: 500000 + (year * 100000), // Mock projection
            projectedIncome: 72000 * Math.pow(1.03, year), // 3% inflation
            projectedSavings: 75000 * year,
            projectedExpenses: 50000 * Math.pow(1.03, year)
          });
        }
        return {
          success: true,
          data: projections
        };
      }
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get financial projections',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Get all wealth data
  getWealthData: async (): Promise<ApiResponse<WealthTrackerData>> => {
    try {
      const [portfolioResponse, payslipResponse] = await Promise.all([
        api.get('/api/portfolio'),
        api.get('/api/payslips?count=50')
      ]);
      
      const wealthData: WealthTrackerData = {
        portfolioData: [], // Mock - would need conversion from backend format
        payslipData: []    // Mock - would need conversion from backend format
      };
      
      return {
        success: true,
        data: wealthData
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get wealth data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Reset data
  resetData: async (): Promise<ApiResponse<void>> => {
    try {
      const response = await api.post('/api/data/reset');
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to reset data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Calculate custom projections for calculator page
  calculateProjections: async (params: {
    target: number;
    monthlyContribution: number;
    returnRate: number;
    years: number;
  }): Promise<ApiResponse<Array<{ month: number; value: number; }>>> => {
    try {
      const response = await api.get(`/api/analysis/projections`, {
        params: {
          target: params.target,
          contributions: params.monthlyContribution,
          rate: params.returnRate,
          years: params.years
        }
      });
      
      if (response.data.success) {
        // Generate monthly projections based on backend response
        const projections = [];
        const monthlyRate = params.returnRate;
        let currentValue = 1000000; // Starting portfolio value - should be dynamic
        
        for (let month = 1; month <= params.years * 12; month++) {
          currentValue = currentValue * (1 + monthlyRate) + params.monthlyContribution;
          projections.push({
            month,
            value: Math.round(currentValue)
          });
          
          // Break if we've reached the target
          if (currentValue >= params.target) {
            break;
          }
        }
        
        return {
          success: true,
          data: projections
        };
      }
      
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to calculate projections',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
};

// Export for backwards compatibility
export default apiService;