// Comprehensive tooltip information for wealth tracker fields and metrics

export interface TooltipData {
  title: string;
  content: string;
}

export const tooltipInfo: { [key: string]: TooltipData } = {
  // Portfolio Data Tooltips
  assetType: {
    title: "Asset Type",
    content: "Select the category of your investment. Stocks include individual shares and ETFs. Bonds are government or corporate debt securities. Crypto includes Bitcoin, Ethereum, and other digital assets. Real Estate covers property investments and REITs. Savings includes cash, CDs, and money market accounts."
  },
  assetValue: {
    title: "Asset Value",
    content: "Enter the current market value of this asset in USD. For stocks, use the current share price × number of shares. For real estate, use current market value or latest appraisal. Update regularly for accurate tracking."
  },
  assetDescription: {
    title: "Asset Description",
    content: "Optional field to add specific details about this asset. Examples: 'Apple AAPL - 100 shares', 'Bitcoin wallet', 'Primary residence', or 'Emergency fund savings account'."
  },

  // Payslip Data Tooltips
  grossSalary: {
    title: "Gross Salary",
    content: "Your total salary before any deductions including taxes, insurance, and retirement contributions. This is your base salary plus any regular allowances or overtime pay for the selected pay period."
  },
  netSalary: {
    title: "Net Salary / Take-Home Pay",
    content: "The amount you actually receive in your bank account after all deductions. This is your gross salary minus taxes, insurance premiums, retirement contributions, and other deductions."
  },
  taxDeductions: {
    title: "Tax Deductions",
    content: "Total amount deducted for federal, state, and local income taxes, plus Social Security and Medicare taxes (FICA). This typically ranges from 20-35% of gross salary depending on your tax bracket and location."
  },
  benefits: {
    title: "Benefits Value",
    content: "Monetary value of employer-provided benefits including health insurance, dental, vision, life insurance, retirement matching, and other perks. Enter the employer's contribution amount, not your share."
  },
  bonuses: {
    title: "Bonuses",
    content: "Additional compensation including performance bonuses, annual bonuses, commissions, profit sharing, or one-time payments. Enter the gross amount before taxes for the selected pay period."
  },
  stockOptions: {
    title: "Stock Options / RSUs",
    content: "Value of stock options, restricted stock units (RSUs), employee stock purchase plans (ESPP), or company stock grants. Use current market value for vested stocks or estimated value for unvested options."
  },
  payPeriod: {
    title: "Pay Period",
    content: "How often you receive your paycheck. Weekly (52 times/year), Bi-weekly (26 times/year), or Monthly (12 times/year). This affects how your annual income is calculated from the entered amounts."
  },

  // Analysis Metrics Tooltips
  totalAssets: {
    title: "Total Assets",
    content: "Sum of all your investments and assets including stocks, bonds, cryptocurrency, real estate, savings accounts, and other valuable holdings. This represents your total wealth before considering any debts or liabilities."
  },
  netWorth: {
    title: "Net Worth",
    content: "Your total assets minus estimated liabilities (debts). This calculation assumes 10% debt ratio as an estimate. For precise net worth, manually subtract your actual debts from total assets. Positive net worth indicates wealth accumulation."
  },
  monthlyIncome: {
    title: "Monthly Income",
    content: "Average monthly take-home pay calculated from your payslip data. This includes salary, bonuses, and other compensation converted to a monthly amount. Used for savings rate calculation and financial projections."
  },
  savingsRate: {
    title: "Savings Rate",
    content: "Percentage of your income being saved and invested. Calculated as estimated monthly savings divided by monthly income. A good savings rate is typically 15-20%. Higher rates accelerate wealth building but ensure you maintain adequate living expenses."
  },
  annualIncome: {
    title: "Annual Income",
    content: "Your total yearly take-home pay including salary, bonuses, stock options, and other compensation. This is calculated by annualizing your pay period data (monthly × 12, bi-weekly × 26, weekly × 52)."
  },
  annualSavings: {
    title: "Annual Savings",
    content: "Estimated amount you save per year based on your savings rate and income. This represents money available for investments, emergency fund building, and wealth accumulation. Actual savings may vary based on expenses and financial goals."
  },
  wealthGrowth: {
    title: "Wealth Growth Indicator",
    content: "Shows whether your wealth is growing (📈 Positive), declining (📉), or building (➡️ Building). Positive indicates net worth exceeds total assets due to investment gains. Building means you're accumulating assets but may have some debt."
  },

  // Asset Breakdown Tooltips
  stocks: {
    title: "Stocks Portfolio",
    content: "Individual company shares, ETFs, mutual funds, and other equity investments. Stocks typically offer higher long-term returns but with greater volatility. Recommended allocation: 60-80% for young investors, 40-60% for older investors."
  },
  bonds: {
    title: "Bonds & Fixed Income",
    content: "Government bonds, corporate bonds, CDs, and other fixed-income securities. Bonds provide stability and regular income but lower returns than stocks. Good for capital preservation and reducing portfolio volatility."
  },
  crypto: {
    title: "Cryptocurrency",
    content: "Bitcoin, Ethereum, and other digital assets. Highly volatile and speculative investments. Financial advisors typically recommend limiting crypto to 5-10% of total portfolio due to extreme price fluctuations."
  },
  real_estate: {
    title: "Real Estate",
    content: "Property investments including primary residence, rental properties, REITs, and real estate funds. Real estate provides inflation protection and diversification but requires significant capital and ongoing management."
  },
  savings: {
    title: "Cash & Savings",
    content: "Bank accounts, money market funds, CDs, and other cash equivalents. Essential for emergency funds (3-6 months expenses) and short-term goals, but earns minimal returns. Keep 10-20% of portfolio in cash."
  },
  other: {
    title: "Other Assets",
    content: "Commodities, precious metals, collectibles, business interests, and alternative investments. These can provide diversification but may be illiquid and harder to value accurately."
  },

  // Projection Tooltips
  projectedWealth: {
    title: "Projected Wealth",
    content: "Estimated future value of your assets assuming 7% annual growth (historical stock market average). This projection includes compound growth of existing assets plus continued savings. Actual results may vary based on market conditions."
  },
  projectedIncome: {
    title: "Projected Income",
    content: "Estimated future income adjusted for 3% annual inflation. This assumes your salary and other income sources will grow to maintain purchasing power. Career progression and job changes may result in higher actual income growth."
  },
  projectedSavings: {
    title: "Projected Savings",
    content: "Estimated annual savings based on projected income and current savings rate. This assumes you maintain the same percentage of income saved. Increasing your savings rate over time can significantly boost wealth accumulation."
  },
  projectedExpenses: {
    title: "Projected Expenses",
    content: "Estimated annual living expenses calculated as projected income minus projected savings. This represents your lifestyle costs adjusted for inflation. Monitor this to ensure expenses don't grow faster than income."
  },

  // Historical Data Tooltips
  entryDate: {
    title: "Entry Date",
    content: "When this financial data was recorded in the system. Historical tracking helps identify trends in income growth, asset accumulation, and spending patterns over time."
  },
  assetGrowth: {
    title: "Asset Growth",
    content: "Change in asset value compared to previous entries. Positive growth indicates successful investing and wealth building. Negative growth may indicate market downturns or withdrawals from investments."
  },
  incomeGrowth: {
    title: "Income Growth",
    content: "Change in monthly income compared to previous entries. Income growth through raises, promotions, or job changes is key to accelerating wealth building. Track this to negotiate better compensation."
  },

  // Chart Tooltips
  chartDataPoint: {
    title: "Data Point",
    content: "Hover over chart elements to see detailed values and percentages. Click on legend items to show/hide data series. Use chart controls to zoom, pan, and analyze different time periods."
  },
  trendLine: {
    title: "Trend Line",
    content: "Shows the general direction of your wealth over time. Upward trends indicate successful wealth building. Flat or downward trends may indicate need to increase savings rate or investment returns."
  },
  targetGoal: {
    title: "Target Goal",
    content: "Your financial goal line shows progress toward specific wealth targets. Set realistic goals based on your timeline and risk tolerance. Adjust savings rate or investment strategy if behind target."
  },

  // Educational and Strategy Tooltips
  investmentGrowth: {
    title: "Investment Growth",
    content: "Percentage increase in investment value over time, including capital appreciation and dividends. Good long-term growth is typically 7-10% annually for diversified portfolios. This metric helps evaluate portfolio performance against benchmarks."
  },
  riskTolerance: {
    title: "Risk Tolerance",
    content: "Your ability to handle investment volatility and potential losses. Higher risk investments can provide better returns but with more fluctuations. Assess based on age, income stability, and personal comfort with market swings."
  },
  diversification: {
    title: "Diversification",
    content: "Strategy of spreading investments across different assets to reduce overall portfolio risk. Don't put all your eggs in one basket. Diversification protects your wealth if one investment performs poorly while others succeed."
  },
  compoundInterest: {
    title: "Compound Interest",
    content: "Interest earned on both principal and previous interest. Albert Einstein called it the eighth wonder of the world. Starting early allows compound interest to significantly multiply wealth over time through exponential growth."
  }
};

// Function to get tooltip info safely
export const getTooltipInfo = (key: string): TooltipData => {
  return tooltipInfo[key] || {
    title: "Information",
    content: "No detailed information available for this field."
  };
};