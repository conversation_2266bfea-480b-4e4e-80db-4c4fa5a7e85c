import { PayslipData } from '../types';

/**
 * Automatic Payslip Data Generator
 * 
 * This utility generates realistic payslip data based on current portfolio value.
 * Used when users don't provide payslip data manually.
 */

interface PortfolioBasedPayslipParams {
  portfolioValue: number;
  monthsOfData?: number;
  assumedAge?: number;
  assumedCareerLevel?: 'entry' | 'mid' | 'senior' | 'executive';
}

interface GeneratedPayslipData extends PayslipData {
  id: string;
  generatedDate: string;
  isGenerated: boolean;
  confidenceLevel: 'high' | 'medium' | 'low';
}

export class PayslipGenerator {
  /**
   * Generate realistic payslip data based on portfolio value
   */
  static generateFromPortfolio(params: PortfolioBasedPayslipParams): GeneratedPayslipData[] {
    const {
      portfolioValue,
      monthsOfData = 12,
      assumedAge = 35,
      assumedCareerLevel = 'mid'
    } = params;

    // Estimate annual salary based on portfolio value
    // Rule of thumb: Portfolio should be 1-10x annual salary depending on age/savings rate
    const estimatedAnnualSalary = this.estimateAnnualSalary(portfolioValue, assumedAge);
    
    // Estimate savings rate based on portfolio size and career level
    const savingsRate = this.estimateSavingsRate(portfolioValue, estimatedAnnualSalary, assumedCareerLevel);
    
    const payslips: GeneratedPayslipData[] = [];
    const baseDate = new Date();
    baseDate.setMonth(baseDate.getMonth() - monthsOfData);

    for (let i = 0; i < monthsOfData; i++) {
      const monthDate = new Date(baseDate);
      monthDate.setMonth(baseDate.getMonth() + i);
      
      // Add some realistic monthly variation (±5%)
      const variation = 0.95 + (Math.random() * 0.1);
      const monthlyGross = (estimatedAnnualSalary / 12) * variation;
      const monthlyNet = monthlyGross * 0.72; // Typical take-home after taxes
      const taxDeductions = monthlyGross * 0.25; // Taxes and other deductions
      const benefits = monthlyGross * 0.03; // Typical benefits

      payslips.push({
        id: `generated-${i}`,
        grossSalary: Math.round(monthlyGross),
        netSalary: Math.round(monthlyNet),
        taxDeductions: Math.round(taxDeductions),
        benefits: Math.round(benefits),
        payPeriod: 'monthly',
        generatedDate: monthDate.toISOString(),
        isGenerated: true,
        confidenceLevel: this.calculateConfidenceLevel(portfolioValue, estimatedAnnualSalary)
      });
    }

    return payslips;
  }

  /**
   * Estimate annual salary based on portfolio value and age
   */
  private static estimateAnnualSalary(portfolioValue: number, age: number): number {
    // Portfolio to salary ratios by age (based on financial planning guidelines)
    const portfolioToSalaryRatios = {
      25: 0.5,   // 0.5x salary
      30: 1.0,   // 1x salary
      35: 3.0,   // 3x salary
      40: 5.0,   // 5x salary
      45: 7.5,   // 7.5x salary
      50: 10.0,  // 10x salary
      55: 12.5,  // 12.5x salary
      60: 15.0   // 15x salary
    };

    // Find the closest age range
    const ages = Object.keys(portfolioToSalaryRatios).map(Number).sort((a, b) => a - b);
    let closestAge = ages[0];
    
    for (const ageKey of ages) {
      if (Math.abs(ageKey - age) < Math.abs(closestAge - age)) {
        closestAge = ageKey;
      }
    }

    const expectedRatio = portfolioToSalaryRatios[closestAge as keyof typeof portfolioToSalaryRatios];
    const estimatedSalary = portfolioValue / expectedRatio;

    // Reasonable bounds (minimum $40k, maximum $500k)
    return Math.max(40000, Math.min(500000, estimatedSalary));
  }

  /**
   * Estimate savings rate based on income level and career stage
   */
  private static estimateSavingsRate(
    portfolioValue: number, 
    annualSalary: number, 
    careerLevel: 'entry' | 'mid' | 'senior' | 'executive'
  ): number {
    const baseSavingsRates = {
      entry: 0.10,     // 10%
      mid: 0.15,       // 15%
      senior: 0.20,    // 20%
      executive: 0.25  // 25%
    };

    let savingsRate = baseSavingsRates[careerLevel];

    // Adjust based on income level
    if (annualSalary > 150000) {
      savingsRate += 0.05; // Higher earners typically save more
    } else if (annualSalary < 60000) {
      savingsRate -= 0.05; // Lower earners have less room to save
    }

    // Adjust based on portfolio size relative to income
    const portfolioToIncomeRatio = portfolioValue / annualSalary;
    if (portfolioToIncomeRatio > 5) {
      savingsRate += 0.05; // Indicates high historical savings rate
    }

    return Math.max(0.05, Math.min(0.35, savingsRate)); // Keep between 5% and 35%
  }

  /**
   * Calculate confidence level of estimates
   */
  private static calculateConfidenceLevel(
    portfolioValue: number, 
    estimatedSalary: number
  ): 'high' | 'medium' | 'low' {
    const ratio = portfolioValue / estimatedSalary;
    
    // Higher confidence for typical ratios
    if (ratio >= 1 && ratio <= 10) {
      return 'high';
    } else if (ratio >= 0.5 && ratio <= 15) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Generate hardcoded realistic portfolio data
   */
  static generateRealisticPortfolioData() {
    return [
      {
        id: 'trow-401k',
        assetType: 'stocks' as const,
        value: 450000,
        description: 'T. Rowe Price 401(k) - Mixed Stock Funds',
        lastUpdated: new Date('2024-01-15')
      },
      {
        id: 'robinhood-stocks',
        assetType: 'stocks' as const,
        value: 180000,
        description: 'Robinhood - Individual Stocks & ETFs',
        lastUpdated: new Date('2024-01-10')
      },
      {
        id: 'etrade-ira',
        assetType: 'stocks' as const,
        value: 120000,
        description: 'E*TRADE Roth IRA - Index Funds',
        lastUpdated: new Date('2024-01-08')
      },
      {
        id: 'savings-emergency',
        assetType: 'savings' as const,
        value: 50000,
        description: 'High-Yield Savings - Emergency Fund',
        lastUpdated: new Date('2024-01-12')
      },
      {
        id: 'crypto-portfolio',
        assetType: 'crypto' as const,
        value: 25000,
        description: 'Cryptocurrency Holdings - BTC, ETH',
        lastUpdated: new Date('2024-01-11')
      },
      {
        id: 'bonds-treasury',
        assetType: 'bonds' as const,
        value: 75000,
        description: 'Treasury Bonds & Corporate Bonds',
        lastUpdated: new Date('2024-01-09')
      }
    ];
  }

  /**
   * Quick generate for simple use cases
   */
  static quickGenerate(portfolioValue: number): GeneratedPayslipData[] {
    return this.generateFromPortfolio({
      portfolioValue,
      monthsOfData: 6, // Last 6 months
      assumedAge: 35,
      assumedCareerLevel: 'mid'
    });
  }
}

export default PayslipGenerator;