import { useState, useCallback } from 'react';
import {
  PortfolioData,
  PayslipData,
  WealthAnalysis,
  FinancialProjection,
  WealthTrackerData
} from '../types';
import { mockApi } from '../services/mockApi';

export interface UseWealthTrackerReturn {
  // Data
  wealthData: WealthTrackerData | null;
  analysis: WealthAnalysis | null;
  projections: FinancialProjection[] | null;
  
  // Loading states
  isLoading: boolean;
  isAddingPortfolio: boolean;
  isAddingPayslip: boolean;
  isAnalyzing: boolean;
  isProjecting: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  addPortfolioData: (data: PortfolioData) => Promise<boolean>;
  addPayslipData: (data: PayslipData) => Promise<boolean>;
  analyzeWealth: () => Promise<boolean>;
  generateProjections: (years: number) => Promise<boolean>;
  loadWealthData: () => Promise<boolean>;
  resetData: () => Promise<boolean>;
  clearError: () => void;
}

export const useWealthTracker = (): UseWealthTrackerReturn => {
  const [wealthData, setWealthData] = useState<WealthTrackerData | null>(null);
  const [analysis, setAnalysis] = useState<WealthAnalysis | null>(null);
  const [projections, setProjections] = useState<FinancialProjection[] | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingPortfolio, setIsAddingPortfolio] = useState(false);
  const [isAddingPayslip, setIsAddingPayslip] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isProjecting, setIsProjecting] = useState(false);
  
  const [error, setError] = useState<string | null>(null);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  const addPortfolioData = useCallback(async (data: PortfolioData): Promise<boolean> => {
    setIsAddingPortfolio(true);
    setError(null);
    
    try {
      const response = await mockApi.addPortfolioData(data);
      
      if (response.success) {
        // Reload wealth data
        await loadWealthData();
        return true;
      } else {
        setError(response.error || 'Failed to add portfolio data');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsAddingPortfolio(false);
    }
  }, []);
  
  const addPayslipData = useCallback(async (data: PayslipData): Promise<boolean> => {
    setIsAddingPayslip(true);
    setError(null);
    
    try {
      const response = await mockApi.addPayslipData(data);
      
      if (response.success) {
        // Reload wealth data
        await loadWealthData();
        return true;
      } else {
        setError(response.error || 'Failed to add payslip data');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsAddingPayslip(false);
    }
  }, []);
  
  const analyzeWealth = useCallback(async (): Promise<boolean> => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const response = await mockApi.getWealthAnalysis();
      
      if (response.success && response.data) {
        setAnalysis(response.data);
        return true;
      } else {
        setError(response.error || 'Failed to analyze wealth');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);
  
  const generateProjections = useCallback(async (years: number): Promise<boolean> => {
    setIsProjecting(true);
    setError(null);
    
    try {
      const response = await mockApi.getFinancialProjections(years);
      
      if (response.success && response.data) {
        setProjections(response.data);
        return true;
      } else {
        setError(response.error || 'Failed to generate projections');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsProjecting(false);
    }
  }, []);
  
  const loadWealthData = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await mockApi.getWealthData();
      
      if (response.success && response.data) {
        setWealthData(response.data);
        if (response.data.analysis) {
          setAnalysis(response.data.analysis);
        }
        if (response.data.projections) {
          setProjections(response.data.projections);
        }
        return true;
      } else {
        setError(response.error || 'Failed to load wealth data');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  const resetData = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await mockApi.resetData();
      
      if (response.success) {
        setWealthData(null);
        setAnalysis(null);
        setProjections(null);
        return true;
      } else {
        setError(response.error || 'Failed to reset data');
        return false;
      }
    } catch (err) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  return {
    // Data
    wealthData,
    analysis,
    projections,
    
    // Loading states
    isLoading,
    isAddingPortfolio,
    isAddingPayslip,
    isAnalyzing,
    isProjecting,
    
    // Error states
    error,
    
    // Actions
    addPortfolioData,
    addPayslipData,
    analyzeWealth,
    generateProjections,
    loadWealthData,
    resetData,
    clearError
  };
};