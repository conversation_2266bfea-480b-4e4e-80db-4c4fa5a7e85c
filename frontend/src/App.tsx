import React, { useEffect, useState } from 'react';
import { useWealthTracker } from './hooks/useWealthTracker';
import WealthTrackerForm from './components/WealthTrackerForm';
import WealthAnalysisDisplay from './components/WealthAnalysisDisplay';
import ProjectionsChart from './components/ProjectionsChart';
import HistoricalDataTable from './components/HistoricalDataTable';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorMessage from './components/ErrorMessage';
import TooltipInfo from './components/TooltipInfo';
import { getTooltipInfo } from './data/tooltipInfo';
import styles from './App.module.css';

const App: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'history' | 'charts'>('dashboard');
  
  const {
    wealthData,
    analysis,
    projections,
    isLoading,
    error,
    loadWealthData,
    clearError,
    analyzeWealth,
    generateProjections
  } = useWealthTracker();

  useEffect(() => {
    loadWealthData();
  }, [loadWealthData]);

  const handleAnalyzeWealth = async () => {
    const success = await analyzeWealth();
    if (success) {
      await generateProjections(10); // Generate 10-year projections
    }
  };

  const handleChartClick = (projection: any) => {
    setActiveTab('charts');
    // Additional navigation logic can be added here
  };

  // Mock historical data for now
  const mockHistoricalData = [
    {
      id: '1',
      date: new Date('2024-01-15'),
      type: 'portfolio' as const,
      data: { assetType: 'stocks' as const, value: 50000, description: 'Tech stocks' },
      summary: 'Added tech stocks portfolio worth $50,000',
      value: 50000
    },
    {
      id: '2', 
      date: new Date('2024-01-20'),
      type: 'payslip' as const,
      data: { grossSalary: 8000, netSalary: 6000, taxDeductions: 1500, benefits: 500, payPeriod: 'monthly' as const },
      summary: 'Monthly salary entry - $6,000 net',
      value: 6000
    },
    {
      id: '3',
      date: new Date('2024-02-01'),
      type: 'analysis' as const,
      data: { totalAssets: 75000, netWorth: 65000, monthlyIncome: 6000, savingsRate: 0.2, annualIncome: 72000, assetBreakdown: {} },
      summary: 'Wealth analysis - $75,000 total assets',
      value: 75000
    }
  ];

  return (
    <div className={styles.app}>
      {/* SVG Filters for Glass Distortion */}
      <svg className={styles.glassFilters}>
        <defs>
          <filter id="glass-distortion">
            <feTurbulence type="turbulence" baseFrequency="0.008" numOctaves="2" result="noise" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="77" />
          </filter>
        </defs>
      </svg>

      <div className={styles.appContainer}>
        <header className={styles.appHeader}>
          <div className={styles.headerGlass}>
            <div className={styles.glassContent}>
              <TooltipInfo
                content="Complete wealth tracking and financial analysis platform with beautiful Apple-style design"
                title="Wealth Tracker Dashboard"
              >
                <h1>💰 Wealth Tracker</h1>
              </TooltipInfo>
              <p>Beautiful financial dashboard with liquid glass design</p>
            </div>
          </div>
          
          {/* Navigation Tabs */}
          <nav className={styles.navigation}>
            <button
              className={`${styles.navTab} ${activeTab === 'dashboard' ? styles.active : ''}`}
              onClick={() => setActiveTab('dashboard')}
              data-testid="dashboard-tab"
            >
              📊 Dashboard
            </button>
            <button
              className={`${styles.navTab} ${activeTab === 'history' ? styles.active : ''}`}
              onClick={() => setActiveTab('history')}
              data-testid="history-tab"
            >
              📈 History
            </button>
            <button
              className={`${styles.navTab} ${activeTab === 'charts' ? styles.active : ''}`}
              onClick={() => setActiveTab('charts')}
              data-testid="charts-tab"
            >
              📊 Charts
            </button>
          </nav>
        </header>

        <main className={styles.appMain}>
          {error && (
            <ErrorMessage 
              message={error} 
              onDismiss={clearError}
              autoDismiss={true}
            />
          )}

          {isLoading ? (
            <LoadingSpinner message="Loading your financial data..." />
          ) : (
            <>
              {activeTab === 'dashboard' && (
                <div className={styles.dashboardGrid}>
                  {/* Input Form Section */}
                  <section className={styles.formSection}>
                    <WealthTrackerForm onDataAdded={handleAnalyzeWealth} />
                  </section>

                  {/* Analysis Display Section */}
                  {analysis && (
                    <section className={styles.analysisSection}>
                      <WealthAnalysisDisplay analysis={analysis} />
                    </section>
                  )}

                  {/* Projections Chart Section */}
                  {projections && projections.length > 0 && (
                    <section className={styles.projectionsSection}>
                      <ProjectionsChart 
                        projections={projections} 
                        onChartClick={handleChartClick}
                      />
                    </section>
                  )}

                  {/* Data Summary Section */}
                  {wealthData && (
                    <section className={styles.summarySection}>
                      <div className={styles.summaryCard}>
                        <div className={styles.glassContent}>
                          <TooltipInfo
                            content="Quick overview of your portfolio size and income sources"
                            title="Portfolio Summary"
                          >
                            <h3>📊 Portfolio Summary</h3>
                          </TooltipInfo>
                          <div className={styles.summaryStats}>
                            <div className={styles.stat}>
                              <TooltipInfo
                                content={getTooltipInfo('totalAssets').content}
                                title={getTooltipInfo('totalAssets').title}
                              >
                                <span className={styles.statLabel}>Assets</span>
                                <span className={styles.statValue}>{wealthData.portfolioData.length}</span>
                              </TooltipInfo>
                            </div>
                            <div className={styles.stat}>
                              <TooltipInfo
                                content="Number of different income sources you have configured"
                                title="Income Sources"
                              >
                                <span className={styles.statLabel}>Income Sources</span>
                                <span className={styles.statValue}>{wealthData.payslipData.length}</span>
                              </TooltipInfo>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>
                  )}
                </div>
              )}

              {activeTab === 'history' && (
                <section className={styles.historySection}>
                  <HistoricalDataTable 
                    entries={mockHistoricalData}
                    onEntryClick={(entry) => console.log('Entry clicked:', entry)}
                    onExport={() => console.log('Export clicked')}
                  />
                </section>
              )}

              {activeTab === 'charts' && (
                <section className={styles.chartsSection}>
                  {projections && projections.length > 0 ? (
                    <div className={styles.chartsGrid}>
                      <ProjectionsChart 
                        projections={projections}
                        onChartClick={handleChartClick}
                      />
                      {/* Additional detailed charts can be added here */}
                    </div>
                  ) : (
                    <div className={styles.emptyChartsState}>
                      <div className={styles.emptyIcon}>📊</div>
                      <h3>No Chart Data Available</h3>
                      <p>Add some financial data to see beautiful charts and projections.</p>
                      <button 
                        className={styles.switchToDashboard}
                        onClick={() => setActiveTab('dashboard')}
                      >
                        Go to Dashboard
                      </button>
                    </div>
                  )}
                </section>
              )}
            </>
          )}
        </main>

        <footer className={styles.appFooter}>
          <div className={styles.footerGlass}>
            <div className={styles.glassContent}>
              <p>Built with React 19 & Liquid Glass Design ✨</p>
              <p>Your financial data stays private and secure 🔒</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default App;