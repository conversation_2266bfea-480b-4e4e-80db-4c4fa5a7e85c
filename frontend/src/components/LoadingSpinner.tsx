import React from 'react';
import styles from './LoadingSpinner.module.css';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  message = 'Loading...',
  size = 'medium'
}) => {
  return (
    <div className={`${styles.loadingSpinner} ${styles[size]}`} data-testid="loading-spinner">
      <div className={styles.spinnerContainer}>
        <div 
          className={styles.spinnerAnimation} 
          data-testid="spinner-animation"
        >
          <div className={styles.spinnerRing}></div>
          <div className={styles.spinnerRing}></div>
          <div className={styles.spinnerRing}></div>
        </div>
        {size !== 'small' && <p className={styles.spinnerMessage}>{message}</p>}
      </div>
    </div>
  );
};

export default LoadingSpinner;