.loadingSpinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
}

.spinnerContainer {
  text-align: center;
  padding: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.spinnerAnimation {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
}

.spinnerRing {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinnerRing:nth-child(1) {
  border-top-color: rgba(255, 255, 255, 0.8);
  animation-delay: 0s;
}

.spinnerRing:nth-child(2) {
  border-right-color: rgba(255, 255, 255, 0.6);
  animation-delay: -0.5s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

.spinnerRing:nth-child(3) {
  border-bottom-color: rgba(255, 255, 255, 0.4);
  animation-delay: -1s;
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spinnerMessage {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Size variations */
.small {
  min-height: 40px;
}

.small .spinnerContainer {
  padding: 8px;
  background: transparent;
  border: none;
  box-shadow: none;
  backdrop-filter: none;
}

.small .spinnerAnimation {
  width: 20px;
  height: 20px;
  margin: 0;
}

.small .spinnerRing {
  border-width: 2px;
}

.medium {
  min-height: 200px;
}

.large {
  min-height: 300px;
}

.large .spinnerContainer {
  padding: 60px;
}

.large .spinnerAnimation {
  width: 80px;
  height: 80px;
  margin: 0 auto 30px;
}

.large .spinnerMessage {
  font-size: 1.3rem;
}