import React from 'react';
import styles from './Navigation.module.css';

export type PageType = 
  | 'dashboard' 
  | 'calculator' 
  | 'portfolio' 
  | 'analytics' 
  | 'history' 
  | 'settings';

interface NavigationProps {
  activePage: PageType;
  onPageChange: (page: PageType) => void;
}

const Navigation: React.FC<NavigationProps> = ({ activePage, onPageChange }) => {
  const pages = [
    { id: 'dashboard' as const, label: '🏠 Dashboard', description: 'Overview and quick actions' },
    { id: 'calculator' as const, label: '🧮 Calculator', description: 'What-if scenarios and projections' },
    { id: 'portfolio' as const, label: '💼 Portfolio', description: 'Detailed asset management' },
    { id: 'analytics' as const, label: '📊 Analytics', description: 'Advanced financial analysis' },
    { id: 'history' as const, label: '📈 History', description: 'Transaction and data history' },
    { id: 'settings' as const, label: '⚙️ Settings', description: 'Configuration and preferences' }
  ];

  return (
    <nav className={styles.navigation} role="navigation" aria-label="Main navigation">
      <div className={styles.navContainer}>
        {pages.map(({ id, label, description }) => (
          <button
            key={id}
            className={`${styles.navButton} ${activePage === id ? styles.active : ''}`}
            onClick={() => onPageChange(id)}
            aria-current={activePage === id ? 'page' : undefined}
            title={description}
            data-testid={`nav-${id}`}
          >
            <span className={styles.navLabel}>{label}</span>
            <span className={styles.navDescription}>{description}</span>
          </button>
        ))}
      </div>
    </nav>
  );
};

export default Navigation;