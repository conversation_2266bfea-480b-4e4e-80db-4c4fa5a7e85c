.calculatorPage {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
}

.pageHeader {
  margin-bottom: 2rem;
}

.headerGlass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.headerGlass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.glassContent {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.glassContent h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glassContent p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.calculatorGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 2rem;
  grid-template-areas: 
    "input results"
    "history history";
}

.inputSection {
  grid-area: input;
}

.resultsSection {
  grid-area: results;
}

.historySection {
  grid-area: history;
}

.sectionCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  height: fit-content;
}

.sectionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.sectionCard h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
}

.calculatorForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inputGroup label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.inputGroup input {
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.inputGroup input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.inputGroup input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.inputError {
  border-color: #ef4444 !important;
  background: rgba(239, 68, 68, 0.1) !important;
}

.errorText {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.calculateButton {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.calculateButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.calculateButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.resultsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.resultCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.resultValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.resultLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.noResults, .noHistory {
  text-align: center;
  padding: 3rem 1rem;
  color: rgba(255, 255, 255, 0.6);
}

.noResultsIcon, .noHistoryIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.errorMessage {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.historyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.clearButton {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clearButton:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.historyItem {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.historyItem:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.historyTime {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 0.5rem;
}

.historyScenario {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.25rem;
}

.historyContribution {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: normal;
}

.historyResult {
  color: #3b82f6;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calculatorGrid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "input"
      "results"
      "history";
    gap: 1.5rem;
  }
  
  .resultsGrid {
    grid-template-columns: 1fr;
  }
  
  .glassContent {
    padding: 1.5rem;
  }
  
  .glassContent h1 {
    font-size: 2rem;
  }
  
  .historyHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .calculatorPage {
    padding: 0.5rem;
  }
  
  .glassContent {
    padding: 1rem;
  }
  
  .glassContent h1 {
    font-size: 1.8rem;
  }
}