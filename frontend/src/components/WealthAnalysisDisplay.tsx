import React from 'react';
import { WealthAnalysis } from '../types';
import styles from './WealthAnalysisDisplay.module.css';

interface WealthAnalysisDisplayProps {
  analysis: WealthAnalysis;
}

const WealthAnalysisDisplay: React.FC<WealthAnalysisDisplayProps> = ({ analysis }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return `${(rate * 100).toFixed(1)}%`;
  };

  const getAssetTypeIcon = (assetType: string) => {
    const icons: { [key: string]: string } = {
      stocks: '📈',
      bonds: '📋',
      crypto: '₿',
      real_estate: '🏠',
      savings: '💳',
      other: '📦'
    };
    return icons[assetType] || '💰';
  };

  return (
    <div className={styles.analysisContainer} data-testid="wealth-analysis-display">
      <div className={styles.glassCard}>
        <div className={styles.cardHeader}>
          <h2>📊 Wealth Analysis</h2>
        </div>
        
        <div className={styles.cardContent}>
          {/* Key Metrics */}
          <div className={styles.metricsGrid}>
            <div className={styles.metric} data-testid="total-assets-metric">
              <div className={styles.metricIcon}>💰</div>
              <div className={styles.metricContent}>
                <span className={styles.metricLabel}>Total Assets</span>
                <span className={styles.metricValue}>
                  {formatCurrency(analysis.totalAssets)}
                </span>
              </div>
            </div>
            
            <div className={styles.metric} data-testid="net-worth-metric">
              <div className={styles.metricIcon}>💎</div>
              <div className={styles.metricContent}>
                <span className={styles.metricLabel}>Net Worth</span>
                <span className={styles.metricValue}>
                  {formatCurrency(analysis.netWorth)}
                </span>
              </div>
            </div>
            
            <div className={styles.metric} data-testid="monthly-income-metric">
              <div className={styles.metricIcon}>💵</div>
              <div className={styles.metricContent}>
                <span className={styles.metricLabel}>Monthly Income</span>
                <span className={styles.metricValue}>
                  {formatCurrency(analysis.monthlyIncome)}
                </span>
              </div>
            </div>
            
            <div className={styles.metric} data-testid="savings-rate-metric">
              <div className={styles.metricIcon}>📈</div>
              <div className={styles.metricContent}>
                <span className={styles.metricLabel}>Savings Rate</span>
                <span className={styles.metricValue}>
                  {formatPercentage(analysis.savingsRate)}
                </span>
              </div>
            </div>
          </div>
          
          {/* Asset Breakdown */}
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>🏆 Asset Breakdown</h3>
            <div className={styles.assetBreakdown} data-testid="asset-breakdown">
              {Object.entries(analysis.assetBreakdown).map(([assetType, value]) => {
                const percentage = (value / analysis.totalAssets) * 100;
                return (
                  <div key={assetType} className={styles.assetItem} data-testid={`asset-${assetType}`}>
                    <div className={styles.assetHeader}>
                      <span className={styles.assetIcon}>
                        {getAssetTypeIcon(assetType)}
                      </span>
                      <span className={styles.assetLabel}>
                        {assetType.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className={styles.assetValue}>
                        {formatCurrency(value)}
                      </span>
                    </div>
                    <div className={styles.progressBar}>
                      <div 
                        className={styles.progressFill}
                        style={{ width: `${percentage}%` }}
                        data-testid={`asset-${assetType}-progress`}
                      ></div>
                    </div>
                    <span className={styles.assetPercentage}>
                      {percentage.toFixed(1)}%
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* Annual Summary */}
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>📅 Annual Summary</h3>
            <div className={styles.summaryGrid}>
              <div className={styles.summaryItem} data-testid="annual-income">
                <span className={styles.summaryLabel}>Annual Income</span>
                <span className={styles.summaryValue}>
                  {formatCurrency(analysis.annualIncome)}
                </span>
              </div>
              <div className={styles.summaryItem} data-testid="annual-savings">
                <span className={styles.summaryLabel}>Annual Savings</span>
                <span className={styles.summaryValue}>
                  {formatCurrency(analysis.annualIncome * analysis.savingsRate)}
                </span>
              </div>
              <div className={styles.summaryItem} data-testid="wealth-growth">
                <span className={styles.summaryLabel}>Wealth Growth</span>
                <span className={styles.summaryValue}>
                  {analysis.netWorth > analysis.totalAssets ? '📈' : analysis.netWorth < 0 ? '📉' : '➡️'}
                  {analysis.netWorth > 0 ? 'Positive' : 'Building'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WealthAnalysisDisplay;