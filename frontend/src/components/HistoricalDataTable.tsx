import React, { useState, useMemo } from 'react';
import { useTable, useSortBy, useFilters, Column } from 'react-table';
import { PortfolioData, PayslipData, WealthAnalysis } from '../types';
import TooltipInfo from './TooltipInfo';
import { getTooltipInfo } from '../data/tooltipInfo';
import styles from './HistoricalDataTable.module.css';

interface HistoricalEntry {
  id: string;
  date: Date;
  type: 'portfolio' | 'payslip' | 'analysis';
  data: PortfolioData | PayslipData | WealthAnalysis;
  summary: string;
  value?: number;
}

interface HistoricalDataTableProps {
  entries: HistoricalEntry[];
  onEntryClick?: (entry: HistoricalEntry) => void;
  onExport?: () => void;
}

const HistoricalDataTable: React.FC<HistoricalDataTableProps> = ({ 
  entries, 
  onEntryClick,
  onExport 
}) => {
  const [filterType, setFilterType] = useState<'all' | 'portfolio' | 'payslip' | 'analysis'>('all');
  const [dateRange, setDateRange] = useState<'all' | '7d' | '30d' | '90d' | '1y'>('all');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'portfolio': return '📊';
      case 'payslip': return '💼';
      case 'analysis': return '📈';
      default: return '📄';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'portfolio': return '#667eea';
      case 'payslip': return '#4ecdc4';
      case 'analysis': return '#45b7d1';
      default: return '#95a5a6';
    }
  };

  const filteredData = useMemo(() => {
    let filtered = entries;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(entry => entry.type === filterType);
    }

    // Filter by date range
    if (dateRange !== 'all') {
      const now = new Date();
      const days = {
        '7d': 7,
        '30d': 30,
        '90d': 90,
        '1y': 365
      }[dateRange] || 0;
      
      const cutoffDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
      filtered = filtered.filter(entry => entry.date >= cutoffDate);
    }

    return filtered;
  }, [entries, filterType, dateRange]);

  const columns: Column<HistoricalEntry>[] = useMemo(
    () => [
      {
        Header: () => (
          <TooltipInfo
            content={getTooltipInfo('entryDate').content}
            title={getTooltipInfo('entryDate').title}
          >
            <span>Date</span>
          </TooltipInfo>
        ),
        accessor: 'date',
        Cell: ({ value }: { value: Date }) => (
          <span className={styles.dateCell}>
            {formatDate(value)}
          </span>
        ),
        sortType: 'datetime'
      },
      {
        Header: 'Type',
        accessor: 'type',
        Cell: ({ value }: { value: string }) => (
          <div className={styles.typeCell}>
            <span 
              className={styles.typeIcon}
              style={{ color: getTypeColor(value) }}
            >
              {getTypeIcon(value)}
            </span>
            <span className={styles.typeLabel}>
              {value.charAt(0).toUpperCase() + value.slice(1)}
            </span>
          </div>
        )
      },
      {
        Header: 'Summary',
        accessor: 'summary',
        Cell: ({ value }: { value: string }) => (
          <span className={styles.summaryCell}>{value}</span>
        )
      },
      {
        Header: () => (
          <TooltipInfo
            content="The monetary value associated with this entry"
            title="Entry Value"
          >
            <span>Value</span>
          </TooltipInfo>
        ),
        accessor: 'value',
        Cell: ({ value }: { value?: number }) => (
          <span className={styles.valueCell}>
            {value ? formatCurrency(value) : '-'}
          </span>
        ),
        sortType: 'basic'
      },
      {
        Header: 'Actions',
        accessor: 'id',
        disableSortBy: true,
        Cell: ({ row }: { row: any }) => (
          <div className={styles.actionsCell}>
            <TooltipInfo
              content="Click to view detailed information about this entry"
              title="View Details"
            >
              <button
                className={styles.actionButton}
                onClick={() => onEntryClick?.(row.original)}
                data-testid={`view-entry-${row.original.id}`}
              >
                👁️
              </button>
            </TooltipInfo>
          </div>
        )
      }
    ],
    [onEntryClick]
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow
  } = useTable(
    {
      columns,
      data: filteredData,
      initialState: {
        sortBy: [{ id: 'date', desc: true }]
      } as any
    },
    useFilters,
    useSortBy
  );

  const getFilterCounts = () => {
    const counts = {
      all: entries.length,
      portfolio: entries.filter(e => e.type === 'portfolio').length,
      payslip: entries.filter(e => e.type === 'payslip').length,
      analysis: entries.filter(e => e.type === 'analysis').length
    };
    return counts;
  };

  const filterCounts = getFilterCounts();

  return (
    <div className={styles.tableContainer} data-testid="historical-data-table">
      <div className={styles.glassCard}>
        <div className={styles.cardHeader}>
          <div className={styles.headerTop}>
            <TooltipInfo
              content="View and analyze your complete financial history with sorting and filtering options"
              title="Historical Data"
            >
              <h2>📅 Financial History</h2>
            </TooltipInfo>
            
            {onExport && (
              <TooltipInfo
                content="Export your historical data to CSV format for external analysis"
                title="Export Data"
              >
                <button 
                  className={styles.exportButton}
                  onClick={onExport}
                  data-testid="export-button"
                >
                  📎 Export
                </button>
              </TooltipInfo>
            )}
          </div>
          
          <div className={styles.filters}>
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Type:</label>
              <div className={styles.filterButtons}>
                {Object.entries(filterCounts).map(([type, count]) => (
                  <button
                    key={type}
                    className={`${styles.filterButton} ${filterType === type ? styles.active : ''}`}
                    onClick={() => setFilterType(type as any)}
                    data-testid={`filter-${type}`}
                  >
                    {type === 'all' ? '📄' : getTypeIcon(type)} 
                    {type.charAt(0).toUpperCase() + type.slice(1)} ({count})
                  </button>
                ))}
              </div>
            </div>
            
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Period:</label>
              <select
                className={styles.filterSelect}
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value as any)}
                data-testid="date-range-filter"
              >
                <option value="all">All Time</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className={styles.tableWrapper}>
          {filteredData.length === 0 ? (
            <div className={styles.emptyState} data-testid="empty-state">
              <div className={styles.emptyIcon}>📄</div>
              <h3>No Data Found</h3>
              <p>No historical entries match your current filters.</p>
              <button 
                className={styles.clearFiltersButton}
                onClick={() => {
                  setFilterType('all');
                  setDateRange('all');
                }}
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <table {...getTableProps()} className={styles.table}>
              <thead className={styles.tableHead}>
                {headerGroups.map(headerGroup => {
                  const { key, ...restHeaderGroupProps } = headerGroup.getHeaderGroupProps();
                  return (
                    <tr key={key} {...restHeaderGroupProps}>
                      {headerGroup.headers.map(column => {
                        const { key, ...restHeaderProps } = column.getHeaderProps(
                          (column as any).getSortByToggleProps()
                        );
                        return (
                          <th key={key} {...restHeaderProps} className={styles.tableHeader}>
                            <div className={styles.headerContent}>
                              {column.render('Header')}
                              {(column as any).canSort && (
                                <span className={styles.sortIcon}>
                                  {(column as any).isSorted
                                    ? (column as any).isSortedDesc
                                      ? ' 🔽'
                                      : ' 🔼'
                                    : ' ↕️'}
                                </span>
                              )}
                            </div>
                          </th>
                        );
                      })}
                    </tr>
                  );
                })}
              </thead>
              <tbody {...getTableBodyProps()} className={styles.tableBody}>
                {rows.map(row => {
                  prepareRow(row);
                  const { key, ...restRowProps } = row.getRowProps();
                  return (
                    <tr 
                      key={key} 
                      {...restRowProps} 
                      className={styles.tableRow}
                      onClick={() => onEntryClick?.(row.original)}
                    >
                      {row.cells.map(cell => {
                        const { key, ...restCellProps } = cell.getCellProps();
                        return (
                          <td key={key} {...restCellProps} className={styles.tableCell}>
                            {cell.render('Cell')}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
        
        <div className={styles.cardFooter}>
          <div className={styles.footerStats}>
            <span className={styles.footerStat}>
              Showing {filteredData.length} of {entries.length} entries
            </span>
            <span className={styles.footerStat}>
              📈 Click any row for details
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HistoricalDataTable;