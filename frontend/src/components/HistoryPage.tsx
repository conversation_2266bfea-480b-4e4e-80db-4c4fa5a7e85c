import React, { useState, useEffect } from 'react';
import TooltipInfo from './TooltipInfo';
import LoadingSpinner from './LoadingSpinner';
import PayslipGenerator from '../utils/payslipGenerator';
import styles from './HistoryPage.module.css';

interface CalculationHistoryItem {
  id: string;
  timestamp: Date;
  type: 'calculation' | 'portfolio_update' | 'payslip_entry' | 'analysis';
  inputs: {
    currentPortfolio?: number;
    targetAmount?: number;
    monthlyContribution?: number;
    annualReturnRate?: number;
    portfolioData?: any;
    payslipData?: any;
  };
  results: {
    monthsToTarget?: number;
    yearsToTarget?: number;
    totalContributions?: number;
    totalGrowth?: number;
    projectedValue?: number;
    confidenceLevel?: 'high' | 'medium' | 'low';
    analysis?: any;
  };
  metadata: {
    source: 'calculator' | 'form_input' | 'generated' | 'manual';
    description: string;
    isGenerated?: boolean;
  };
}

interface HistoryFilters {
  timeRange: 'today' | 'week' | 'month' | 'year' | 'all';
  type: 'all' | 'calculation' | 'portfolio_update' | 'payslip_entry' | 'analysis';
  source: 'all' | 'calculator' | 'form_input' | 'generated' | 'manual';
}

const HistoryPage: React.FC = () => {
  const [historyItems, setHistoryItems] = useState<CalculationHistoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<CalculationHistoryItem[]>([]);
  const [filters, setFilters] = useState<HistoryFilters>({
    timeRange: 'all',
    type: 'all',
    source: 'all'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<CalculationHistoryItem | null>(null);
  const [showingDetailedView, setShowingDetailedView] = useState(false);

  // Load historical data from multiple sources
  useEffect(() => {
    loadHistoricalData();
  }, []);

  // Apply filters whenever filters or history items change
  useEffect(() => {
    applyFilters();
  }, [filters, historyItems]);

  const loadHistoricalData = async () => {
    setIsLoading(true);
    try {
      const allHistoryItems: CalculationHistoryItem[] = [];

      // 1. Load calculation history from Calculator page
      const calculationHistory = localStorage.getItem('calculationHistory');
      if (calculationHistory) {
        const calculations = JSON.parse(calculationHistory);
        calculations.forEach((calc: any) => {
          allHistoryItems.push({
            id: `calc-${calc.id}`,
            timestamp: new Date(calc.timestamp),
            type: 'calculation',
            inputs: calc.inputs,
            results: calc.results,
            metadata: {
              source: 'calculator',
              description: `Financial scenario: $${calc.inputs.currentPortfolio?.toLocaleString()} → $${calc.inputs.targetAmount?.toLocaleString()}`,
              isGenerated: false
            }
          });
        });
      }

      // 2. Load portfolio updates (using generated realistic data)
      const portfolioData = PayslipGenerator.generateRealisticPortfolioData();
      portfolioData.forEach((portfolio, index) => {
        allHistoryItems.push({
          id: `portfolio-${index}`,
          timestamp: portfolio.lastUpdated,
          type: 'portfolio_update',
          inputs: {
            portfolioData: portfolio
          },
          results: {
            projectedValue: portfolio.value,
            confidenceLevel: 'high'
          },
          metadata: {
            source: 'generated',
            description: `${portfolio.description} - $${portfolio.value.toLocaleString()}`,
            isGenerated: true
          }
        });
      });

      // 3. Load generated payslip data
      const generatedPayslips = PayslipGenerator.quickGenerate(900000); // Based on total portfolio
      generatedPayslips.forEach((payslip, index) => {
        allHistoryItems.push({
          id: `payslip-${index}`,
          timestamp: new Date(payslip.generatedDate),
          type: 'payslip_entry',
          inputs: {
            payslipData: payslip
          },
          results: {
            projectedValue: payslip.grossSalary * 12, // Annual salary
            confidenceLevel: payslip.confidenceLevel
          },
          metadata: {
            source: 'generated',
            description: `Monthly income: $${payslip.grossSalary.toLocaleString()} gross, $${payslip.netSalary.toLocaleString()} net`,
            isGenerated: true
          }
        });
      });

      // 4. Add some analysis entries
      allHistoryItems.push({
        id: 'analysis-wealth',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        type: 'analysis',
        inputs: {},
        results: {
          projectedValue: 900000,
          confidenceLevel: 'high',
          analysis: {
            netWorth: 810000, // 90% of portfolio (accounting for debts)
            savingsRate: 0.18,
            monthsToFI: 148 // Financial independence
          }
        },
        metadata: {
          source: 'generated',
          description: 'Comprehensive wealth analysis - $810K net worth, 18% savings rate',
          isGenerated: true
        }
      });

      // Sort by timestamp (newest first)
      allHistoryItems.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      setHistoryItems(allHistoryItems);
    } catch (error) {
      console.error('Failed to load historical data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...historyItems];

    // Time range filter
    const now = new Date();
    const timeFilters = {
      today: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
      week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      month: new Date(now.getFullYear(), now.getMonth(), 1),
      year: new Date(now.getFullYear(), 0, 1),
      all: new Date(0)
    };

    if (filters.timeRange !== 'all') {
      const cutoffDate = timeFilters[filters.timeRange];
      filtered = filtered.filter(item => item.timestamp >= cutoffDate);
    }

    // Type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    // Source filter
    if (filters.source !== 'all') {
      filtered = filtered.filter(item => item.metadata.source === filters.source);
    }

    setFilteredItems(filtered);
  };

  const clearHistory = () => {
    localStorage.removeItem('calculationHistory');
    setHistoryItems(prev => prev.filter(item => item.metadata.source !== 'calculator'));
  };

  const exportHistory = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalEntries: filteredItems.length,
      entries: filteredItems
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `wealth-tracker-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      calculation: '🧮',
      portfolio_update: '💼',
      payslip_entry: '💰',
      analysis: '📊'
    };
    return icons[type as keyof typeof icons] || '📄';
  };

  const getSourceBadge = (source: string, isGenerated: boolean) => {
    if (isGenerated) return '🤖 Generated';
    
    const badges = {
      calculator: '🧮 Calculator',
      form_input: '📝 Manual',
      generated: '🤖 Auto',
      manual: '✏️ User'
    };
    return badges[source as keyof typeof badges] || source;
  };

  const getConfidenceBadge = (level?: string) => {
    if (!level) return null;
    
    const badges = {
      high: { text: '✅ High', class: styles.confidenceHigh },
      medium: { text: '⚠️ Medium', class: styles.confidenceMedium },
      low: { text: '❌ Low', class: styles.confidenceLow }
    };
    
    const badge = badges[level as keyof typeof badges];
    return badge ? <span className={badge.class}>{badge.text}</span> : null;
  };

  const showItemDetails = (item: CalculationHistoryItem) => {
    setSelectedItem(item);
    setShowingDetailedView(true);
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading your financial history..." />;
  }

  return (
    <div className={styles.historyPage} data-testid="history-page">
      <div className={styles.pageHeader}>
        <div className={styles.headerGlass}>
          <div className={styles.glassContent}>
            <TooltipInfo
              title="Financial History"
              content="Complete history of your calculations, inputs, portfolio updates, and analysis results"
            >
              <h1>📈 Financial History</h1>
            </TooltipInfo>
            <p>Track all your financial data inputs and calculation results</p>
          </div>
        </div>
      </div>

      <div className={styles.historyControls}>
        <div className={styles.filterSection}>
          <div className={styles.filterGroup}>
            <label htmlFor="time-filter">Time Range:</label>
            <select 
              id="time-filter"
              value={filters.timeRange}
              onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value as any }))}
              data-testid="time-filter"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="year">Last Year</option>
            </select>
          </div>

          <div className={styles.filterGroup}>
            <label htmlFor="type-filter">Type:</label>
            <select 
              id="type-filter"
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value as any }))}
              data-testid="type-filter"
            >
              <option value="all">All Types</option>
              <option value="calculation">Calculations</option>
              <option value="portfolio_update">Portfolio</option>
              <option value="payslip_entry">Income</option>
              <option value="analysis">Analysis</option>
            </select>
          </div>

          <div className={styles.filterGroup}>
            <label htmlFor="source-filter">Source:</label>
            <select 
              id="source-filter"
              value={filters.source}
              onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value as any }))}
              data-testid="source-filter"
            >
              <option value="all">All Sources</option>
              <option value="calculator">Calculator</option>
              <option value="form_input">Manual Input</option>
              <option value="generated">Generated</option>
            </select>
          </div>
        </div>

        <div className={styles.actionButtons}>
          <TooltipInfo content="Export your financial history as JSON">
            <button 
              onClick={exportHistory} 
              className={styles.exportButton}
              data-testid="export-button"
            >
              📤 Export
            </button>
          </TooltipInfo>
          
          <TooltipInfo content="Clear calculator history (keeps portfolio and income data)">
            <button 
              onClick={clearHistory} 
              className={styles.clearButton}
              data-testid="clear-button"
            >
              🗑️ Clear Calculator History
            </button>
          </TooltipInfo>
        </div>
      </div>

      <div className={styles.historyStats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{filteredItems.length}</div>
          <div className={styles.statLabel}>Total Entries</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {filteredItems.filter(item => item.type === 'calculation').length}
          </div>
          <div className={styles.statLabel}>Calculations</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {filteredItems.filter(item => item.metadata.isGenerated).length}
          </div>
          <div className={styles.statLabel}>Auto-Generated</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {filteredItems.filter(item => !item.metadata.isGenerated).length}
          </div>
          <div className={styles.statLabel}>Manual Entries</div>
        </div>
      </div>

      <div className={styles.historyList} data-testid="history-list">
        {filteredItems.length === 0 ? (
          <div className={styles.emptyState} data-testid="empty-history">
            <div className={styles.emptyIcon}>📊</div>
            <h3>No History Found</h3>
            <p>No entries match your current filters. Try adjusting the time range or type filters.</p>
          </div>
        ) : (
          filteredItems.map((item) => (
            <div 
              key={item.id} 
              className={styles.historyItem}
              onClick={() => showItemDetails(item)}
              data-testid={`history-item-${item.id}`}
            >
              <div className={styles.itemHeader}>
                <div className={styles.itemIcon}>
                  {getTypeIcon(item.type)}
                </div>
                <div className={styles.itemTitle}>
                  <h3>{item.metadata.description}</h3>
                  <div className={styles.itemMeta}>
                    <span className={styles.timestamp}>
                      {item.timestamp.toLocaleDateString()} {item.timestamp.toLocaleTimeString()}
                    </span>
                    <span className={styles.sourceBadge}>
                      {getSourceBadge(item.metadata.source, item.metadata.isGenerated || false)}
                    </span>
                    {getConfidenceBadge(item.results.confidenceLevel)}
                  </div>
                </div>
              </div>

              <div className={styles.itemPreview}>
                {item.type === 'calculation' && (
                  <div className={styles.calculationPreview}>
                    <span>Target in {item.results.yearsToTarget} years</span>
                    <span>${item.results.totalContributions?.toLocaleString()} contributions</span>
                  </div>
                )}
                
                {item.type === 'portfolio_update' && (
                  <div className={styles.portfolioPreview}>
                    <span>{item.inputs.portfolioData?.assetType}</span>
                    <span>${item.results.projectedValue?.toLocaleString()}</span>
                  </div>
                )}
                
                {item.type === 'payslip_entry' && (
                  <div className={styles.payslipPreview}>
                    <span>Monthly: ${item.inputs.payslipData?.grossSalary.toLocaleString()}</span>
                    <span>Annual: ${item.results.projectedValue?.toLocaleString()}</span>
                  </div>
                )}
                
                {item.type === 'analysis' && (
                  <div className={styles.analysisPreview}>
                    <span>Net Worth: ${item.results.analysis?.netWorth.toLocaleString()}</span>
                    <span>Savings Rate: {(item.results.analysis?.savingsRate * 100).toFixed(1)}%</span>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Detailed View Modal */}
      {showingDetailedView && selectedItem && (
        <div className={styles.modalOverlay} onClick={() => setShowingDetailedView(false)}>
          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
            <div className={styles.modalHeader}>
              <h2>{getTypeIcon(selectedItem.type)} {selectedItem.metadata.description}</h2>
              <button 
                className={styles.closeButton}
                onClick={() => setShowingDetailedView(false)}
                data-testid="close-modal"
              >
                ✕
              </button>
            </div>
            
            <div className={styles.modalBody}>
              <div className={styles.detailSection}>
                <h3>📥 Inputs</h3>
                <pre className={styles.jsonDisplay}>
                  {JSON.stringify(selectedItem.inputs, null, 2)}
                </pre>
              </div>
              
              <div className={styles.detailSection}>
                <h3>📤 Results</h3>
                <pre className={styles.jsonDisplay}>
                  {JSON.stringify(selectedItem.results, null, 2)}
                </pre>
              </div>
              
              <div className={styles.detailSection}>
                <h3>ℹ️ Metadata</h3>
                <pre className={styles.jsonDisplay}>
                  {JSON.stringify(selectedItem.metadata, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryPage;