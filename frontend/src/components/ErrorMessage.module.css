.errorMessage {
  margin-bottom: 20px;
  animation: slideIn 0.3s ease-out;
}

.errorContainer {
  background: linear-gradient(135deg, 
    rgba(255, 99, 99, 0.2) 0%, 
    rgba(255, 150, 150, 0.1) 100%);
  border: 1px solid rgba(255, 99, 99, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(16px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.errorContent {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.errorIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.errorText {
  flex: 1;
  color: #ffffff;
  font-weight: 500;
}

.closeButton {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.closeButton:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.closeButton:active {
  transform: scale(0.95);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}