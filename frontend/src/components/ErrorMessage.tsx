import React, { useEffect } from 'react';
import styles from './ErrorMessage.module.css';

interface ErrorMessageProps {
  message: string;
  onDismiss: () => void;
  autoDismiss?: boolean;
  autoDismissTimeout?: number;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message, 
  onDismiss,
  autoDismiss = false,
  autoDismissTimeout = 5000
}) => {
  useEffect(() => {
    if (autoDismiss) {
      const timer = setTimeout(() => {
        onDismiss();
      }, autoDismissTimeout);

      return () => clearTimeout(timer);
    }
  }, [autoDismiss, autoDismissTimeout, onDismiss]);

  return (
    <div className={styles.errorMessage} data-testid="error-message">
      <div className={styles.errorContainer}>
        <div className={styles.errorContent}>
          <div className={styles.errorIcon} data-testid="error-icon">
            ⚠️
          </div>
          <span className={styles.errorText}>{message}</span>
          <button 
            className={styles.closeButton} 
            onClick={onDismiss}
            data-testid="close-button"
            aria-label="Close error message"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorMessage;