.tooltipTrigger {
  display: inline-block;
  cursor: help;
}

.tooltip {
  position: fixed;
  z-index: 1000;
  pointer-events: none;
  transform: translate(-50%, -100%);
  animation: tooltipFadeIn 0.2s ease-out;
}

.tooltip.bottom {
  transform: translate(-50%, 0);
}

.tooltip.left {
  transform: translate(-100%, -50%);
}

.tooltip.right {
  transform: translate(0, -50%);
}

.tooltipGlass {
  position: relative;
  max-width: 300px;
  min-width: 200px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.tooltipGlass::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  z-index: 1;
  pointer-events: none;
}

.tooltipContent {
  position: relative;
  z-index: 2;
  padding: 16px;
  color: #ffffff;
}

.tooltipTitle {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 6px;
}

.tooltipBody {
  font-size: 0.8rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

.tooltipArrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  z-index: 3;
}

.tooltip.top .tooltipArrow {
  bottom: -5px;
  left: 50%;
  margin-left: -4px;
  border-top: none;
  border-left: none;
}

.tooltip.bottom .tooltipArrow {
  top: -5px;
  left: 50%;
  margin-left: -4px;
  border-bottom: none;
  border-right: none;
}

.tooltip.left .tooltipArrow {
  right: -5px;
  top: 50%;
  margin-top: -4px;
  border-left: none;
  border-bottom: none;
}

.tooltip.right .tooltipArrow {
  left: -5px;
  top: 50%;
  margin-top: -4px;
  border-right: none;
  border-top: none;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
}

.tooltip.bottom {
  animation: tooltipFadeInBottom 0.2s ease-out;
}

@keyframes tooltipFadeInBottom {
  from {
    opacity: 0;
    transform: translate(-50%, 0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
  }
}

.tooltip.left {
  animation: tooltipFadeInLeft 0.2s ease-out;
}

@keyframes tooltipFadeInLeft {
  from {
    opacity: 0;
    transform: translate(-100%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-100%, -50%) scale(1);
  }
}

.tooltip.right {
  animation: tooltipFadeInRight 0.2s ease-out;
}

@keyframes tooltipFadeInRight {
  from {
    opacity: 0;
    transform: translate(0, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(0, -50%) scale(1);
  }
}