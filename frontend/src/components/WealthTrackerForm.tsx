import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { PortfolioData, PayslipData } from '../types';
import { useWealthTracker } from '../hooks/useWealthTracker';
import styles from './WealthTrackerForm.module.css';

interface WealthTrackerFormProps {
  onDataAdded: () => void;
}

interface FormData {
  // Portfolio fields
  assetType: 'stocks' | 'bonds' | 'crypto' | 'real_estate' | 'savings' | 'other';
  assetValue: number;
  assetDescription?: string;
  
  // Payslip fields
  grossSalary: number;
  netSalary: number;
  taxDeductions: number;
  benefits: number;
  bonuses?: number;
  stockOptions?: number;
  payPeriod: 'monthly' | 'bi-weekly' | 'weekly';
}

const WealthTrackerForm: React.FC<WealthTrackerFormProps> = ({ onDataAdded }) => {
  const [activeTab, setActiveTab] = useState<'portfolio' | 'payslip'>('portfolio');
  const { addPortfolioData, addPayslipData, isAddingPortfolio, isAddingPayslip } = useWealthTracker();
  
  const { register, handleSubmit, reset, formState: { errors } } = useForm<FormData>();

  const onSubmit = async (data: FormData) => {
    let success = false;
    
    if (activeTab === 'portfolio') {
      const portfolioData: PortfolioData = {
        assetType: data.assetType,
        value: data.assetValue,
        description: data.assetDescription
      };
      success = await addPortfolioData(portfolioData);
    } else {
      const payslipData: PayslipData = {
        grossSalary: data.grossSalary,
        netSalary: data.netSalary,
        taxDeductions: data.taxDeductions,
        benefits: data.benefits,
        bonuses: data.bonuses,
        stockOptions: data.stockOptions,
        payPeriod: data.payPeriod
      };
      success = await addPayslipData(payslipData);
    }
    
    if (success) {
      reset();
      onDataAdded();
    }
  };

  const isLoading = isAddingPortfolio || isAddingPayslip;

  return (
    <div className={styles.formContainer} data-testid="wealth-tracker-form">
      <div className={styles.glassCard}>
        <div className={styles.cardHeader}>
          <h2>💰 Add Financial Data</h2>
          <div className={styles.tabContainer}>
            <button
              type="button"
              className={`${styles.tab} ${activeTab === 'portfolio' ? styles.active : ''}`}
              onClick={() => setActiveTab('portfolio')}
              data-testid="portfolio-tab"
            >
              📊 Portfolio
            </button>
            <button
              type="button"
              className={`${styles.tab} ${activeTab === 'payslip' ? styles.active : ''}`}
              onClick={() => setActiveTab('payslip')}
              data-testid="payslip-tab"
            >
              💼 Income
            </button>
          </div>
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
          {activeTab === 'portfolio' && (
            <div className={styles.formSection} data-testid="portfolio-form">
              <div className={styles.formGroup}>
                <label className={styles.label}>Asset Type</label>
                <select
                  {...register('assetType', { required: 'Asset type is required' })}
                  className={styles.select}
                  data-testid="asset-type-select"
                >
                  <option value="">Select asset type...</option>
                  <option value="stocks">📈 Stocks</option>
                  <option value="bonds">📋 Bonds</option>
                  <option value="crypto">₿ Cryptocurrency</option>
                  <option value="real_estate">🏠 Real Estate</option>
                  <option value="savings">💳 Savings</option>
                  <option value="other">📦 Other</option>
                </select>
                {errors.assetType && (
                  <span className={styles.error} data-testid="asset-type-error">
                    {errors.assetType.message}
                  </span>
                )}
              </div>
              
              <div className={styles.formGroup}>
                <label className={styles.label}>Value ($)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('assetValue', { 
                    required: 'Asset value is required',
                    min: { value: 0, message: 'Value must be positive' }
                  })}
                  className={styles.input}
                  placeholder="Enter asset value..."
                  data-testid="asset-value-input"
                />
                {errors.assetValue && (
                  <span className={styles.error} data-testid="asset-value-error">
                    {errors.assetValue.message}
                  </span>
                )}
              </div>
              
              <div className={styles.formGroup}>
                <label className={styles.label}>Description (Optional)</label>
                <input
                  type="text"
                  {...register('assetDescription')}
                  className={styles.input}
                  placeholder="Describe this asset..."
                  data-testid="asset-description-input"
                />
              </div>
            </div>
          )}
          
          {activeTab === 'payslip' && (
            <div className={styles.formSection} data-testid="payslip-form">
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.label}>Gross Salary ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('grossSalary', { 
                      required: 'Gross salary is required',
                      min: { value: 0, message: 'Salary must be positive' }
                    })}
                    className={styles.input}
                    placeholder="Gross salary..."
                    data-testid="gross-salary-input"
                  />
                  {errors.grossSalary && (
                    <span className={styles.error}>{errors.grossSalary.message}</span>
                  )}
                </div>
                
                <div className={styles.formGroup}>
                  <label className={styles.label}>Net Salary ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('netSalary', { 
                      required: 'Net salary is required',
                      min: { value: 0, message: 'Salary must be positive' }
                    })}
                    className={styles.input}
                    placeholder="Net salary..."
                    data-testid="net-salary-input"
                  />
                  {errors.netSalary && (
                    <span className={styles.error}>{errors.netSalary.message}</span>
                  )}
                </div>
              </div>
              
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.label}>Tax Deductions ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('taxDeductions', { 
                      required: 'Tax deductions required',
                      min: { value: 0, message: 'Must be positive' }
                    })}
                    className={styles.input}
                    placeholder="Tax deductions..."
                    data-testid="tax-deductions-input"
                  />
                  {errors.taxDeductions && (
                    <span className={styles.error}>{errors.taxDeductions.message}</span>
                  )}
                </div>
                
                <div className={styles.formGroup}>
                  <label className={styles.label}>Benefits ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('benefits', { 
                      required: 'Benefits amount required',
                      min: { value: 0, message: 'Must be positive' }
                    })}
                    className={styles.input}
                    placeholder="Benefits..."
                    data-testid="benefits-input"
                  />
                  {errors.benefits && (
                    <span className={styles.error}>{errors.benefits.message}</span>
                  )}
                </div>
              </div>
              
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.label}>Bonuses ($) - Optional</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('bonuses')}
                    className={styles.input}
                    placeholder="Bonuses..."
                    data-testid="bonuses-input"
                  />
                </div>
                
                <div className={styles.formGroup}>
                  <label className={styles.label}>Stock Options ($) - Optional</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('stockOptions')}
                    className={styles.input}
                    placeholder="Stock options..."
                    data-testid="stock-options-input"
                  />
                </div>
              </div>
              
              <div className={styles.formGroup}>
                <label className={styles.label}>Pay Period</label>
                <select
                  {...register('payPeriod', { required: 'Pay period is required' })}
                  className={styles.select}
                  data-testid="pay-period-select"
                >
                  <option value="">Select pay period...</option>
                  <option value="weekly">📅 Weekly</option>
                  <option value="bi-weekly">📅 Bi-weekly</option>
                  <option value="monthly">📅 Monthly</option>
                </select>
                {errors.payPeriod && (
                  <span className={styles.error}>{errors.payPeriod.message}</span>
                )}
              </div>
            </div>
          )}
          
          <div className={styles.formActions}>
            <button
              type="submit"
              disabled={isLoading}
              className={styles.submitButton}
              data-testid="submit-button"
            >
              {isLoading ? (
                <span>⏳ Adding...</span>
              ) : (
                <span>✨ Add {activeTab === 'portfolio' ? 'Asset' : 'Income'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WealthTrackerForm;