.navigation {
  padding: 1rem 0;
  margin-bottom: 2rem;
}

.navContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  max-width: 1200px;
  margin: 0 auto;
}

.navButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.navButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.navButton:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.navButton:hover::before {
  opacity: 1;
}

.navButton.active {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.15) 0%, 
    rgba(147, 51, 234, 0.15) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 
    0 8px 32px rgba(59, 130, 246, 0.2),
    0 2px 8px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.navLabel {
  font-size: 1rem;
  font-weight: 600;
  color: inherit;
  line-height: 1.2;
}

.navDescription {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
  transition: color 0.3s ease;
}

.navButton.active .navDescription {
  color: rgba(59, 130, 246, 0.8);
}

.navButton:hover .navDescription {
  color: rgba(255, 255, 255, 0.9);
}

/* Responsive design */
@media (max-width: 768px) {
  .navContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .navButton {
    padding: 0.75rem;
  }
  
  .navLabel {
    font-size: 0.9rem;
  }
  
  .navDescription {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .navContainer {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* Glass effect enhancement */
.navButton {
  position: relative;
  z-index: 1;
}

.navButton::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 30%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 15px 15px 0 0;
  pointer-events: none;
  opacity: 0.7;
}