.analysisContainer {
  width: 100%;
  max-width: 800px;
}

.glassCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.cardHeader {
  padding: 24px 24px 0;
  text-align: center;
}

.cardHeader h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.cardContent {
  padding: 0 24px 24px;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.2s ease;
  cursor: help;
}

.metric:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metricIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.metricContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metricLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
}

.metricValue {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 700;
}

.section {
  margin-bottom: 32px;
}

.sectionTitle {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.assetBreakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assetItem {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: help;
}

.assetItem:hover {
  background: rgba(255, 255, 255, 0.08);
}

.assetHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.assetIcon {
  font-size: 1.1rem;
}

.assetLabel {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
  flex: 1;
  margin-left: 8px;
}

.assetValue {
  color: #ffffff;
  font-weight: 600;
}

.progressBar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.assetPercentage {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  font-weight: 500;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: help;
}

.summaryItem:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.summaryLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
}

.summaryValue {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

@media (max-width: 640px) {
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .summaryGrid {
    grid-template-columns: 1fr;
  }
  
  .cardContent {
    padding: 0 16px 16px;
  }
  
  .cardHeader {
    padding: 16px 16px 0;
  }
}