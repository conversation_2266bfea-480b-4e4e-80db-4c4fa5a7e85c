.historyPage {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
}

.pageHeader {
  margin-bottom: 2rem;
}

.headerGlass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.headerGlass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.glassContent {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.glassContent h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glassContent p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.historyControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.filterSection {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.filterGroup select {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-width: 120px;
}

.filterGroup select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.actionButtons {
  display: flex;
  gap: 1rem;
}

.exportButton, .clearButton {
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.exportButton {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
}

.exportButton:hover {
  background: rgba(34, 197, 94, 0.2);
  transform: translateY(-1px);
}

.clearButton {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.clearButton:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

.historyStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.historyItem {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.historyItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 25%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.06) 0%, 
    rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.historyItem:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.itemHeader {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.itemIcon {
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.itemTitle {
  flex: 1;
}

.itemTitle h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.3;
}

.itemMeta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.timestamp {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.sourceBadge {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.confidenceHigh {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.confidenceMedium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.confidenceLow {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.itemPreview {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.calculationPreview, .portfolioPreview, .payslipPreview, .analysisPreview {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.calculationPreview span, .portfolioPreview span, .payslipPreview span, .analysisPreview span {
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.6);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.emptyState p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.modalContent {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 800px;
  max-height: 80vh;
  width: 90%;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #ffffff;
}

.closeButton {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(239, 68, 68, 0.2);
}

.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.detailSection {
  margin-bottom: 2rem;
}

.detailSection h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
}

.jsonDisplay {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  color: #94a3b8;
  overflow-x: auto;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .historyControls {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }
  
  .filterSection {
    flex-direction: column;
    gap: 1rem;
  }
  
  .actionButtons {
    justify-content: center;
  }
  
  .historyStats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .itemHeader {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .itemPreview {
    flex-direction: column;
    gap: 1rem;
  }
  
  .calculationPreview, .portfolioPreview, .payslipPreview, .analysisPreview {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .modalContent {
    width: 95%;
    max-height: 90vh;
  }
  
  .glassContent h1 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .historyPage {
    padding: 0.5rem;
  }
  
  .historyStats {
    grid-template-columns: 1fr;
  }
  
  .glassContent {
    padding: 1.5rem;
  }
  
  .historyItem {
    padding: 1rem;
  }
}