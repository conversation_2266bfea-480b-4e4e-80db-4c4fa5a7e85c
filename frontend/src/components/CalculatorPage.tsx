import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import TooltipInfo from './TooltipInfo';
import LoadingSpinner from './LoadingSpinner';
import { apiService } from '../services/apiService';
import styles from './CalculatorPage.module.css';

interface CalculatorInputs {
  currentPortfolio: number;
  targetAmount: number;
  monthlyContribution: number;
  annualReturnRate: number;
  timeHorizonYears?: number;
}

interface CalculationResult {
  monthsToTarget: number;
  yearsToTarget: number;
  totalContributions: number;
  totalGrowth: number;
  finalAmount: number;
  contributionShare: number;
  growthShare: number;
}

interface CalculationHistory {
  id: string;
  timestamp: Date;
  inputs: CalculatorInputs;
  results: CalculationResult;
}

const CalculatorPage: React.FC = () => {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [calculationHistory, setCalculationHistory] = useState<CalculationHistory[]>([]);
  const [error, setError] = useState<string>('');

  const { register, handleSubmit, watch, formState: { errors }, setValue } = useForm<CalculatorInputs>({
    defaultValues: {
      currentPortfolio: 1000000,
      targetAmount: 3000000,
      monthlyContribution: 3000,
      annualReturnRate: 7,
      timeHorizonYears: 15
    }
  });

  // Watch all form values for real-time updates
  const watchedValues = watch();

  // Load calculation history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('calculationHistory');
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        setCalculationHistory(parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      } catch (error) {
        console.error('Failed to parse calculation history:', error);
      }
    }
  }, []);

  // Save calculation history to localStorage
  const saveCalculationHistory = (newHistory: CalculationHistory[]) => {
    setCalculationHistory(newHistory);
    localStorage.setItem('calculationHistory', JSON.stringify(newHistory));
  };

  // Real-time calculation as user types
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (watchedValues.currentPortfolio && 
          watchedValues.targetAmount && 
          watchedValues.monthlyContribution && 
          watchedValues.annualReturnRate) {
        performCalculation(watchedValues, false); // Silent calculation for preview
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(debounceTimer);
  }, [watchedValues]);

  const performCalculation = async (inputs: CalculatorInputs, saveToHistory: boolean = true) => {
    if (saveToHistory) {
      setIsCalculating(true);
    }
    setError('');

    try {
      // Convert annual rate to monthly
      const monthlyRate = inputs.annualReturnRate / 100 / 12;

      // Calculate months to target using our backend API
      const response = await apiService.calculateProjections({
        target: inputs.targetAmount,
        monthlyContribution: inputs.monthlyContribution,
        returnRate: monthlyRate,
        years: inputs.timeHorizonYears || 15
      });

      if (!response.success || !response.data) {
        throw new Error('Failed to calculate projections');
      }

      // Find the projection that reaches our target
      const targetProjection = response.data.find((p: any) => p.value >= inputs.targetAmount);
      const monthsToTarget = targetProjection ? targetProjection.month : 0;

      // Calculate additional metrics
      const totalContributions = inputs.monthlyContribution * monthsToTarget;
      const totalGrowth = inputs.targetAmount - inputs.currentPortfolio - totalContributions;
      const contributionShare = totalContributions / (inputs.targetAmount - inputs.currentPortfolio);
      const growthShare = totalGrowth / (inputs.targetAmount - inputs.currentPortfolio);

      const calculationResult: CalculationResult = {
        monthsToTarget,
        yearsToTarget: Math.round((monthsToTarget / 12) * 10) / 10,
        totalContributions,
        totalGrowth,
        finalAmount: inputs.targetAmount,
        contributionShare: Math.max(0, Math.min(1, contributionShare)),
        growthShare: Math.max(0, Math.min(1, growthShare))
      };

      setResult(calculationResult);

      // Save to history if this is a deliberate calculation
      if (saveToHistory) {
        const newHistoryItem: CalculationHistory = {
          id: Date.now().toString(),
          timestamp: new Date(),
          inputs: { ...inputs },
          results: calculationResult
        };

        const updatedHistory = [newHistoryItem, ...calculationHistory.slice(0, 9)]; // Keep last 10
        saveCalculationHistory(updatedHistory);
      }

    } catch (error) {
      setError('Failed to calculate projections. Please check your inputs.');
      console.error('Calculation error:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const onSubmit = async (data: CalculatorInputs) => {
    await performCalculation(data, true);
  };

  const clearHistory = () => {
    setCalculationHistory([]);
    localStorage.removeItem('calculationHistory');
  };

  const loadHistoryItem = (historyItem: CalculationHistory) => {
    setValue('currentPortfolio', historyItem.inputs.currentPortfolio);
    setValue('targetAmount', historyItem.inputs.targetAmount);
    setValue('monthlyContribution', historyItem.inputs.monthlyContribution);
    setValue('annualReturnRate', historyItem.inputs.annualReturnRate);
    setValue('timeHorizonYears', historyItem.inputs.timeHorizonYears);
    setResult(historyItem.results);
  };

  return (
    <div className={styles.calculatorPage} data-testid="calculator-page">
      <div className={styles.pageHeader}>
        <div className={styles.headerGlass}>
          <div className={styles.glassContent}>
            <TooltipInfo
              title="Financial Calculator"
              content="Perform what-if scenarios and see real-time projections as you adjust your inputs"
            >
              <h1>🧮 Financial Calculator</h1>
            </TooltipInfo>
            <p>Dynamic wealth projections with real-time updates</p>
          </div>
        </div>
      </div>

      <div className={styles.calculatorGrid}>
        {/* Input Form */}
        <div className={styles.inputSection}>
          <div className={styles.sectionCard}>
            <div className={styles.glassContent}>
              <h2>💰 Scenario Inputs</h2>
              <form onSubmit={handleSubmit(onSubmit)} className={styles.calculatorForm}>
                <div className={styles.inputGroup}>
                  <TooltipInfo
                    title="Current Portfolio Value"
                    content="Your total current investment portfolio value across all accounts"
                  >
                    <label htmlFor="currentPortfolio">Current Portfolio ($)</label>
                  </TooltipInfo>
                  <input
                    id="currentPortfolio"
                    type="number"
                    step="1000"
                    {...register('currentPortfolio', { 
                      required: 'Current portfolio is required',
                      min: { value: 0, message: 'Must be positive' }
                    })}
                    className={errors.currentPortfolio ? styles.inputError : ''}
                    data-testid="current-portfolio-input"
                  />
                  {errors.currentPortfolio && <span className={styles.errorText}>{errors.currentPortfolio.message}</span>}
                </div>

                <div className={styles.inputGroup}>
                  <TooltipInfo
                    title="Target Amount"
                    content="Your financial goal - the amount you want to reach"
                  >
                    <label htmlFor="targetAmount">Target Amount ($)</label>
                  </TooltipInfo>
                  <input
                    id="targetAmount"
                    type="number"
                    step="1000"
                    {...register('targetAmount', { 
                      required: 'Target amount is required',
                      min: { value: 1, message: 'Must be positive' }
                    })}
                    className={errors.targetAmount ? styles.inputError : ''}
                    data-testid="target-amount-input"
                  />
                  {errors.targetAmount && <span className={styles.errorText}>{errors.targetAmount.message}</span>}
                </div>

                <div className={styles.inputGroup}>
                  <TooltipInfo
                    title="Monthly Contribution"
                    content="How much you plan to invest each month"
                  >
                    <label htmlFor="monthlyContribution">Monthly Contribution ($)</label>
                  </TooltipInfo>
                  <input
                    id="monthlyContribution"
                    type="number"
                    step="100"
                    {...register('monthlyContribution', { 
                      required: 'Monthly contribution is required',
                      min: { value: 0, message: 'Must be positive' }
                    })}
                    className={errors.monthlyContribution ? styles.inputError : ''}
                    data-testid="monthly-contribution-input"
                  />
                  {errors.monthlyContribution && <span className={styles.errorText}>{errors.monthlyContribution.message}</span>}
                </div>

                <div className={styles.inputGroup}>
                  <TooltipInfo
                    title="Annual Return Rate"
                    content="Expected annual return rate as a percentage (e.g., 7 for 7%)"
                  >
                    <label htmlFor="annualReturnRate">Annual Return Rate (%)</label>
                  </TooltipInfo>
                  <input
                    id="annualReturnRate"
                    type="number"
                    step="0.1"
                    min="0"
                    max="30"
                    {...register('annualReturnRate', { 
                      required: 'Return rate is required',
                      min: { value: 0, message: 'Must be positive' },
                      max: { value: 30, message: 'Must be realistic (≤30%)' }
                    })}
                    className={errors.annualReturnRate ? styles.inputError : ''}
                    data-testid="return-rate-input"
                  />
                  {errors.annualReturnRate && <span className={styles.errorText}>{errors.annualReturnRate.message}</span>}
                </div>

                <button 
                  type="submit" 
                  className={styles.calculateButton}
                  disabled={isCalculating}
                  data-testid="calculate-button"
                >
                  {isCalculating ? <LoadingSpinner size="small" /> : '🧮 Calculate & Save'}
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Results Display */}
        <div className={styles.resultsSection}>
          <div className={styles.sectionCard}>
            <div className={styles.glassContent}>
              <h2>📊 Live Results</h2>
              {error && <div className={styles.errorMessage} data-testid="error-message">{error}</div>}
              
              {result ? (
                <div className={styles.resultsGrid} data-testid="calculation-results">
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>{result.yearsToTarget}</div>
                    <div className={styles.resultLabel}>Years to Target</div>
                  </div>
                  
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>{result.monthsToTarget}</div>
                    <div className={styles.resultLabel}>Months to Target</div>
                  </div>
                  
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>${result.totalContributions.toLocaleString()}</div>
                    <div className={styles.resultLabel}>Total Contributions</div>
                  </div>
                  
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>${result.totalGrowth.toLocaleString()}</div>
                    <div className={styles.resultLabel}>Investment Growth</div>
                  </div>
                  
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>{(result.contributionShare * 100).toFixed(1)}%</div>
                    <div className={styles.resultLabel}>From Contributions</div>
                  </div>
                  
                  <div className={styles.resultCard}>
                    <div className={styles.resultValue}>{(result.growthShare * 100).toFixed(1)}%</div>
                    <div className={styles.resultLabel}>From Growth</div>
                  </div>
                </div>
              ) : (
                <div className={styles.noResults} data-testid="no-results">
                  <div className={styles.noResultsIcon}>🧮</div>
                  <p>Enter your financial scenario above to see live calculations</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Calculation History */}
        <div className={styles.historySection}>
          <div className={styles.sectionCard}>
            <div className={styles.glassContent}>
              <div className={styles.historyHeader}>
                <h2>📈 Calculation History</h2>
                {calculationHistory.length > 0 && (
                  <button 
                    onClick={clearHistory} 
                    className={styles.clearButton}
                    data-testid="clear-history-button"
                  >
                    Clear History
                  </button>
                )}
              </div>
              
              {calculationHistory.length > 0 ? (
                <div className={styles.historyList} data-testid="calculation-history">
                  {calculationHistory.map((item) => (
                    <div 
                      key={item.id} 
                      className={styles.historyItem}
                      onClick={() => loadHistoryItem(item)}
                      data-testid={`history-item-${item.id}`}
                    >
                      <div className={styles.historyTime}>
                        {item.timestamp.toLocaleDateString()} {item.timestamp.toLocaleTimeString()}
                      </div>
                      <div className={styles.historyScenario}>
                        ${item.inputs.currentPortfolio.toLocaleString()} → ${item.inputs.targetAmount.toLocaleString()}
                        <span className={styles.historyContribution}>
                          (${item.inputs.monthlyContribution.toLocaleString()}/mo @ {item.inputs.annualReturnRate}%)
                        </span>
                      </div>
                      <div className={styles.historyResult}>
                        <strong>{item.results.yearsToTarget} years</strong>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noHistory} data-testid="no-history">
                  <div className={styles.noHistoryIcon}>📊</div>
                  <p>No calculations yet. Perform a calculation to see it here!</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalculatorPage;