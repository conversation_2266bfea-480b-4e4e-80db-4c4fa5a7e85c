{"name": "er", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "api:dev": "ts-node src/api/server.ts", "api:build": "tsc && node dist/api/server.js", "api:start": "node dist/api/server.js", "frontend:dev": "cd frontend && npm start", "fullstack:dev": "concurrently \"npm run api:dev\" \"npm run frontend:dev\"", "fullstack:build": "npm run build && cd frontend && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"readline": "^1.3.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5"}, "devDependencies": {"typescript": "^5.5.3", "ts-node": "^10.9.0", "@types/node": "^20.0.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jest": "^29.5.0", "jest": "^29.5.0", "ts-jest": "^29.1.0"}, "private": true}